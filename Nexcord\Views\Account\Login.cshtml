@model Nexcord.ViewModels.LoginViewModel
@{
    ViewData["Title"] = "Login - NEXCORD";
    Layout = "_Layout";
}

<div class="auth-container">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-5 col-md-7">
                <div class="auth-card">
                    <div class="auth-header">
                        <div class="auth-logo">
                            <i class="fas fa-gamepad"></i>
                        </div>
                        <h2 class="auth-title">Welcome Back</h2>
                        <p class="auth-subtitle">Sign in to your gaming account</p>
                    </div>

                    <form asp-action="Login" method="post" class="auth-form">
                        <div asp-validation-summary="All" class="text-danger mb-3"></div>
                        
                        <div class="form-group">
                            <label asp-for="UsernameOrEmail" class="form-label">
                                <i class="fas fa-user"></i>
                                Username or Email
                            </label>
                            <input asp-for="UsernameOrEmail" class="form-control auth-input" placeholder="Enter your username or email" />
                            <span asp-validation-for="UsernameOrEmail" class="text-danger"></span>
                        </div>

                        <div class="form-group">
                            <label asp-for="Password" class="form-label">
                                <i class="fas fa-lock"></i>
                                Password
                            </label>
                            <input asp-for="Password" class="form-control auth-input" placeholder="Enter your password" />
                            <span asp-validation-for="Password" class="text-danger"></span>
                        </div>

                        <div class="form-group">
                            <div class="form-check">
                                <input asp-for="RememberMe" class="form-check-input" />
                                <label asp-for="RememberMe" class="form-check-label">
                                    Remember me
                                </label>
                            </div>
                        </div>

                        <button type="submit" class="btn-auth btn-primary">
                            <i class="fas fa-sign-in-alt"></i>
                            Sign In
                        </button>
                    </form>

                    <div class="auth-footer">
                        <p>Don't have an account? 
                            <a asp-action="Register" class="auth-link">Create one here</a>
                        </p>
                        <a href="#" class="auth-link">Forgot your password?</a>
                    </div>

                    <div class="auth-divider">
                        <span>or continue with</span>
                    </div>

                    <div class="social-login">
                        <button class="btn-social btn-discord">
                            <i class="fab fa-discord"></i>
                            Discord
                        </button>
                        <button class="btn-social btn-steam">
                            <i class="fab fa-steam"></i>
                            Steam
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
