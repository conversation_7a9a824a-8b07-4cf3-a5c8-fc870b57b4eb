@model Nexcord.ViewModels.UserSearchViewModel
@{
    ViewData["Title"] = "Find Friends";
}

<div class="friend-search-container">
    <div class="search-header">
        <h2><i class="fas fa-user-plus"></i> Find Friends</h2>
        <p>Search for friends by username, email, or display name</p>
    </div>

    <div class="search-form-container">
        <form asp-action="Search" method="post" class="search-form">
            <div class="search-input-group">
                <input asp-for="SearchTerm" class="search-input" placeholder="Enter username, email, or display name..." autocomplete="off" />
                <button type="submit" class="search-btn">
                    <i class="fas fa-search"></i>
                    Search
                </button>
            </div>
            <span asp-validation-for="SearchTerm" class="text-danger"></span>
        </form>
    </div>

    @if (Model.HasSearched)
    {
        <div class="search-results">
            <div class="results-header">
                <h3>Search Results</h3>
                <span class="results-count">@Model.Results.Count result(s) found</span>
            </div>

            @if (Model.Results.Any())
            {
                <div class="user-results-list">
                    @foreach (var user in Model.Results)
                    {
                        <div class="user-result-card" data-user-id="@user.Id">
                            <div class="user-avatar">
                                @if (!string.IsNullOrEmpty(user.AvatarUrl))
                                {
                                    <img src="@user.AvatarUrl" alt="@user.DisplayName" />
                                }
                                else
                                {
                                    <div class="avatar-placeholder">
                                        <i class="fas fa-user"></i>
                                    </div>
                                }
                                @if (user.IsOnline)
                                {
                                    <div class="status-indicator online"></div>
                                }
                            </div>

                            <div class="user-info">
                                <div class="user-name">
                                    <h4>@user.DisplayName</h4>
                                    <span class="username">@@@user.Username</span>
                                </div>
                                <div class="user-status">
                                    @if (user.IsOnline)
                                    {
                                        <span class="status online">Online</span>
                                    }
                                    else
                                    {
                                        <span class="status offline">Last seen @user.LastActive.ToString("MMM dd, yyyy")</span>
                                    }
                                </div>
                            </div>

                            <div class="user-actions">
                                @switch (user.FriendStatus)
                                {
                                    case Nexcord.ViewModels.FriendStatus.NotFriend:
                                        <button class="btn btn-primary send-request-btn" data-user-id="@user.Id">
                                            <i class="fas fa-user-plus"></i>
                                            Add Friend
                                        </button>
                                        break;
                                    case Nexcord.ViewModels.FriendStatus.PendingOutgoing:
                                        <button class="btn btn-secondary" disabled>
                                            <i class="fas fa-clock"></i>
                                            Request Sent
                                        </button>
                                        break;
                                    case Nexcord.ViewModels.FriendStatus.PendingIncoming:
                                        <button class="btn btn-success accept-request-btn" data-friendship-id="@user.FriendshipId">
                                            <i class="fas fa-check"></i>
                                            Accept
                                        </button>
                                        break;
                                    case Nexcord.ViewModels.FriendStatus.Friends:
                                        <a href="@Url.Action("Chat", new { friendId = user.Id })" class="btn btn-success">
                                            <i class="fas fa-comments"></i>
                                            Message
                                        </a>
                                        break;
                                    case Nexcord.ViewModels.FriendStatus.Blocked:
                                        <button class="btn btn-danger" disabled>
                                            <i class="fas fa-ban"></i>
                                            Blocked
                                        </button>
                                        break;
                                }
                            </div>
                        </div>
                    }
                </div>
            }
            else
            {
                <div class="no-results">
                    <i class="fas fa-search"></i>
                    <h3>No users found</h3>
                    <p>Try searching with a different username, email, or display name.</p>
                </div>
            }
        </div>
    }
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            // Send friend request
            $('.send-request-btn').click(function() {
                const userId = $(this).data('user-id');
                const button = $(this);
                
                button.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Sending...');
                
                $.ajax({
                    url: '@Url.Action("SendRequest")',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({ UserId: userId }),
                    success: function(response) {
                        if (response.success) {
                            button.removeClass('btn-primary').addClass('btn-secondary')
                                  .html('<i class="fas fa-clock"></i> Request Sent')
                                  .prop('disabled', true);
                            showNotification(response.message, 'success');
                        } else {
                            button.prop('disabled', false).html('<i class="fas fa-user-plus"></i> Add Friend');
                            showNotification(response.message, 'error');
                        }
                    },
                    error: function() {
                        button.prop('disabled', false).html('<i class="fas fa-user-plus"></i> Add Friend');
                        showNotification('An error occurred. Please try again.', 'error');
                    }
                });
            });

            // Accept friend request
            $('.accept-request-btn').click(function() {
                const friendshipId = $(this).data('friendship-id');
                const button = $(this);
                
                button.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Accepting...');
                
                $.ajax({
                    url: '@Url.Action("AcceptRequest")',
                    type: 'POST',
                    data: { friendshipId: friendshipId },
                    success: function(response) {
                        if (response.success) {
                            const userId = button.closest('.user-result-card').data('user-id');
                            button.removeClass('btn-success').addClass('btn-success')
                                  .html('<i class="fas fa-comments"></i> Message')
                                  .prop('disabled', false)
                                  .attr('onclick', `window.location.href='@Url.Action("Chat")?friendId=${userId}'`);
                            showNotification(response.message, 'success');
                        } else {
                            button.prop('disabled', false).html('<i class="fas fa-check"></i> Accept');
                            showNotification(response.message, 'error');
                        }
                    },
                    error: function() {
                        button.prop('disabled', false).html('<i class="fas fa-check"></i> Accept');
                        showNotification('An error occurred. Please try again.', 'error');
                    }
                });
            });

            // Auto-focus search input
            $('.search-input').focus();
        });

        function showNotification(message, type) {
            // Create notification element
            const notification = $(`
                <div class="notification ${type}">
                    <i class="fas ${type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle'}"></i>
                    ${message}
                </div>
            `);
            
            // Add to page
            $('body').append(notification);
            
            // Show and auto-hide
            setTimeout(() => notification.addClass('show'), 100);
            setTimeout(() => {
                notification.removeClass('show');
                setTimeout(() => notification.remove(), 300);
            }, 3000);
        }
    </script>
}

<style>
.friend-search-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
}

.search-header {
    text-align: center;
    margin-bottom: 30px;
}

.search-header h2 {
    color: var(--nexcord-text);
    margin-bottom: 10px;
}

.search-header p {
    color: var(--nexcord-text-muted);
}

.search-form-container {
    margin-bottom: 30px;
}

.search-input-group {
    display: flex;
    gap: 10px;
    max-width: 500px;
    margin: 0 auto;
}

.search-input {
    flex: 1;
    padding: 12px 16px;
    border: 2px solid var(--nexcord-dark-lighter);
    border-radius: 8px;
    background: var(--nexcord-dark);
    color: var(--nexcord-text);
    font-size: 16px;
}

.search-input:focus {
    outline: none;
    border-color: var(--nexcord-primary);
}

.search-btn {
    padding: 12px 24px;
    background: var(--nexcord-primary);
    color: white;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: background 0.2s ease;
}

.search-btn:hover {
    background: var(--nexcord-primary-dark);
}

.search-results {
    margin-top: 30px;
}

.results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--nexcord-dark-lighter);
}

.results-header h3 {
    color: var(--nexcord-text);
    margin: 0;
}

.results-count {
    color: var(--nexcord-text-muted);
    font-size: 14px;
}

.user-results-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.user-result-card {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 16px;
    background: var(--nexcord-dark-lighter);
    border-radius: 8px;
    transition: background 0.2s ease;
}

.user-result-card:hover {
    background: rgba(255, 255, 255, 0.05);
}

.user-avatar {
    position: relative;
    width: 48px;
    height: 48px;
    border-radius: 50%;
    overflow: hidden;
}

.user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.avatar-placeholder {
    width: 100%;
    height: 100%;
    background: var(--nexcord-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
}

.status-indicator {
    position: absolute;
    bottom: -2px;
    right: -2px;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    border: 3px solid var(--nexcord-dark-lighter);
}

.status-indicator.online {
    background: #43b581;
}

.user-info {
    flex: 1;
}

.user-name h4 {
    color: var(--nexcord-text);
    margin: 0 0 4px 0;
    font-size: 16px;
    font-weight: 600;
}

.username {
    color: var(--nexcord-text-muted);
    font-size: 14px;
}

.user-status {
    margin-top: 4px;
}

.status {
    font-size: 12px;
    padding: 2px 6px;
    border-radius: 4px;
}

.status.online {
    color: #43b581;
}

.status.offline {
    color: var(--nexcord-text-muted);
}

.user-actions {
    display: flex;
    gap: 8px;
}

.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 6px;
    transition: all 0.2s ease;
}

.btn-primary {
    background: var(--nexcord-primary);
    color: white;
}

.btn-primary:hover {
    background: var(--nexcord-primary-dark);
}

.btn-secondary {
    background: var(--nexcord-text-muted);
    color: white;
}

.btn-success {
    background: #43b581;
    color: white;
}

.btn-success:hover {
    background: #369870;
}

.btn-danger {
    background: #f04747;
    color: white;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.no-results {
    text-align: center;
    padding: 60px 20px;
    color: var(--nexcord-text-muted);
}

.no-results i {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
}

.no-results h3 {
    margin-bottom: 8px;
    color: var(--nexcord-text);
}

.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 20px;
    border-radius: 8px;
    color: white;
    font-weight: 600;
    z-index: 1000;
    transform: translateX(100%);
    transition: transform 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.notification.show {
    transform: translateX(0);
}

.notification.success {
    background: #43b581;
}

.notification.error {
    background: #f04747;
}
</style>
