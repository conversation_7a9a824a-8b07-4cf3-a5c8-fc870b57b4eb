using System.Diagnostics;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using Nexcord.Models;
using Nexcord.Data;
using Nexcord.ViewModels;

namespace Nexcord.Controllers;

public class HomeController : Controller
{
    private readonly ILogger<HomeController> _logger;
    private readonly ApplicationDbContext _context;

    public HomeController(ILogger<HomeController> logger, ApplicationDbContext context)
    {
        _logger = logger;
        _context = context;
    }

    public IActionResult Index()
    {
        if (User.Identity?.IsAuthenticated == true)
        {
            return RedirectToAction("Dashboard");
        }
        return View();
    }

    [Authorize]
    public async Task<IActionResult> Dashboard()
    {
        var userId = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value;
        var user = await _context.Users
            .Include(u => u.Profile)
            .Include(u => u.ServerMemberships)
                .ThenInclude(sm => sm.Server)
            .FirstOrDefaultAsync(u => u.Id == userId);

        if (user == null)
        {
            return RedirectToAction("Login", "Account");
        }

        // Get friend data for the dashboard
        var friendships = await _context.Friendships
            .Include(f => f.Requester).ThenInclude(u => u.Profile)
            .Include(f => f.Addressee).ThenInclude(u => u.Profile)
            .Where(f => (f.RequesterId == userId || f.AddresseeId == userId))
            .ToListAsync();

        // Get accepted friends
        var acceptedFriendships = friendships.Where(f => f.Status == FriendshipStatus.Accepted).ToList();
        var friends = acceptedFriendships.Select(f =>
        {
            var friend = f.GetOtherUser(userId!);
            return new FriendViewModel
            {
                Id = friend.Id,
                Username = friend.UserName ?? "",
                DisplayName = friend.DisplayName,
                FullName = friend.FullName,
                AvatarUrl = friend.Profile?.AvatarUrl,
                IsOnline = friend.IsOnline,
                LastActive = friend.LastActive,
                FriendsSince = f.AcceptedAt ?? f.CreatedAt
            };
        }).OrderBy(f => f.Username).ToList();

        // Get pending incoming requests
        var pendingRequests = friendships
            .Where(f => f.Status == FriendshipStatus.Pending && f.AddresseeId == userId)
            .Select(f => new FriendRequestViewModel
            {
                FriendshipId = f.Id,
                UserId = f.RequesterId,
                Username = f.Requester.UserName ?? "",
                DisplayName = f.Requester.DisplayName,
                FullName = f.Requester.FullName,
                AvatarUrl = f.Requester.Profile?.AvatarUrl,
                IsOnline = f.Requester.IsOnline,
                RequestedAt = f.CreatedAt,
                IsIncoming = true
            }).OrderByDescending(r => r.RequestedAt).ToList();

        // Get recent direct messages
        var recentMessages = await _context.DirectMessages
            .Where(dm => (dm.SenderId == userId || dm.ReceiverId == userId))
            .OrderByDescending(dm => dm.CreatedAt)
            .Take(10)
            .Include(dm => dm.Sender).ThenInclude(u => u.Profile)
            .Include(dm => dm.Receiver).ThenInclude(u => u.Profile)
            .ToListAsync();

        var dashboardViewModel = new DashboardViewModel
        {
            User = user,
            Friends = friends.Take(8).ToList(), // Show only first 8 friends
            OnlineFriendsCount = friends.Count(f => f.IsOnline),
            TotalFriendsCount = friends.Count,
            PendingRequests = pendingRequests.Take(5).ToList(), // Show only first 5 requests
            PendingRequestsCount = pendingRequests.Count,
            RecentMessages = recentMessages.Select(dm => new DirectMessageViewModel
            {
                Id = dm.Id,
                Content = dm.Content,
                CreatedAt = dm.CreatedAt,
                IsFromCurrentUser = dm.SenderId == userId,
                SenderName = dm.Sender.DisplayName,
                SenderAvatarUrl = dm.Sender.Profile?.AvatarUrl
            }).ToList()
        };

        return View(dashboardViewModel);
    }

    public IActionResult Privacy()
    {
        return View();
    }

    [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
    public IActionResult Error()
    {
        return View(new ErrorViewModel { RequestId = Activity.Current?.Id ?? HttpContext.TraceIdentifier });
    }
}
