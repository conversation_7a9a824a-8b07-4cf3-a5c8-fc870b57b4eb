using System.ComponentModel.DataAnnotations;

namespace Nexcord.Models
{
    public class Server
    {
        public int Id { get; set; }

        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;

        [StringLength(500)]
        public string? Description { get; set; }

        [StringLength(255)]
        public string? ImageUrl { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public bool IsPublic { get; set; } = true;

        [Required]
        public string OwnerId { get; set; } = string.Empty;

        // Gaming-specific properties
        public string? GameType { get; set; } // CS2, Dota2, GTAV, etc.
        public int MaxMembers { get; set; } = 100;

        // Navigation properties
        public virtual ApplicationUser Owner { get; set; } = null!;
        public virtual ICollection<Channel> Channels { get; set; } = new List<Channel>();
        public virtual ICollection<ServerMember> Members { get; set; } = new List<ServerMember>();
    }
}
