using Microsoft.AspNetCore.SignalR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using Nexcord.Data;
using Nexcord.Models;
using System.Security.Claims;

namespace Nexcord.Hubs
{
    [Authorize]
    public class ChatHub : Hub
    {
        private readonly ApplicationDbContext _context;

        public ChatHub(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task JoinServer(string serverId)
        {
            await Groups.AddToGroupAsync(Context.ConnectionId, $"Server_{serverId}");
        }

        public async Task LeaveServer(string serverId)
        {
            await Groups.RemoveFromGroupAsync(Context.ConnectionId, $"Server_{serverId}");
        }

        public async Task JoinChannel(string channelId)
        {
            await Groups.AddToGroupAsync(Context.ConnectionId, $"Channel_{channelId}");
        }

        public async Task LeaveChannel(string channelId)
        {
            await Groups.RemoveFromGroupAsync(Context.ConnectionId, $"Channel_{channelId}");
        }

        public async Task SendMessage(string channelId, string content)
        {
            var userId = Context.User?.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (userId == null) return;

            var user = await _context.Users
                .Include(u => u.Profile)
                .FirstOrDefaultAsync(u => u.Id == userId);

            if (user == null) return;

            var channel = await _context.Channels
                .Include(c => c.Server)
                .FirstOrDefaultAsync(c => c.Id == int.Parse(channelId));

            if (channel == null) return;

            // Check if user is member of the server
            var isMember = await _context.ServerMembers
                .AnyAsync(sm => sm.UserId == userId && sm.ServerId == channel.ServerId);

            if (!isMember) return;

            var message = new Message
            {
                Content = content,
                UserId = userId,
                ChannelId = int.Parse(channelId),
                CreatedAt = DateTime.UtcNow
            };

            _context.Messages.Add(message);
            await _context.SaveChangesAsync();

            // Send message to all users in the channel
            await Clients.Group($"Channel_{channelId}").SendAsync("ReceiveMessage", new
            {
                id = message.Id,
                content = message.Content,
                createdAt = message.CreatedAt,
                user = new
                {
                    id = user.Id,
                    userName = user.UserName,
                    fullName = user.FullName,
                    avatarUrl = user.Profile?.AvatarUrl
                }
            });
        }

        public async Task SendDirectMessage(string receiverId, string content)
        {
            var senderId = Context.User?.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (senderId == null) return;

            var sender = await _context.Users
                .Include(u => u.Profile)
                .FirstOrDefaultAsync(u => u.Id == senderId);

            if (sender == null) return;

            var directMessage = new DirectMessage
            {
                Content = content,
                SenderId = senderId,
                ReceiverId = receiverId,
                CreatedAt = DateTime.UtcNow
            };

            _context.DirectMessages.Add(directMessage);
            await _context.SaveChangesAsync();

            // Send to both sender and receiver
            var messageData = new
            {
                id = directMessage.Id,
                content = directMessage.Content,
                createdAt = directMessage.CreatedAt,
                senderId = senderId,
                receiverId = receiverId,
                sender = new
                {
                    id = sender.Id,
                    userName = sender.UserName,
                    fullName = sender.FullName,
                    avatarUrl = sender.Profile?.AvatarUrl
                }
            };

            await Clients.User(senderId).SendAsync("ReceiveDirectMessage", messageData);
            await Clients.User(receiverId).SendAsync("ReceiveDirectMessage", messageData);
        }

        public async Task UpdateUserStatus(bool isOnline)
        {
            var userId = Context.User?.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (userId == null) return;

            var user = await _context.Users.FindAsync(userId);
            if (user != null)
            {
                user.IsOnline = isOnline;
                user.LastActive = DateTime.UtcNow;
                await _context.SaveChangesAsync();

                // Get user's friends to notify them of status change
                var friendships = await _context.Friendships
                    .Where(f => (f.RequesterId == userId || f.AddresseeId == userId) &&
                               f.Status == FriendshipStatus.Accepted)
                    .ToListAsync();

                var friendIds = friendships.Select(f =>
                    f.RequesterId == userId ? f.AddresseeId : f.RequesterId).ToList();

                // Notify friends about status change
                foreach (var friendId in friendIds)
                {
                    await Clients.User(friendId).SendAsync("FriendStatusChanged", new
                    {
                        userId = userId,
                        isOnline = isOnline,
                        lastActive = user.LastActive
                    });
                }
            }
        }

        public async Task NotifyFriendRequest(string addresseeId, object requestData)
        {
            await Clients.User(addresseeId).SendAsync("FriendRequestReceived", requestData);
        }

        public async Task NotifyFriendRequestAccepted(string requesterId, object friendData)
        {
            await Clients.User(requesterId).SendAsync("FriendRequestAccepted", friendData);
        }

        public override async Task OnConnectedAsync()
        {
            var userId = Context.User?.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (userId != null)
            {
                await UpdateUserStatus(true);
            }
            await base.OnConnectedAsync();
        }

        public override async Task OnDisconnectedAsync(Exception? exception)
        {
            var userId = Context.User?.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (userId != null)
            {
                await UpdateUserStatus(false);
            }
            await base.OnDisconnectedAsync(exception);
        }
    }
}
