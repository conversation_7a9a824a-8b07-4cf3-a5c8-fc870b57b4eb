using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Nexcord.Models;

namespace Nexcord.Data
{
    public class ApplicationDbContext : IdentityDbContext<ApplicationUser>
    {
        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options)
            : base(options)
        {
        }

        public DbSet<Server> Servers { get; set; }
        public DbSet<Channel> Channels { get; set; }
        public DbSet<Message> Messages { get; set; }
        public DbSet<ServerMember> ServerMembers { get; set; }
        public DbSet<DirectMessage> DirectMessages { get; set; }
        public DbSet<UserProfile> UserProfiles { get; set; }
        public DbSet<Friendship> Friendships { get; set; }

        protected override void OnModelCreating(ModelBuilder builder)
        {
            base.OnModelCreating(builder);

            // Server configuration
            builder.Entity<Server>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).IsRequired().HasMaxLength(100);
                entity.Property(e => e.Description).HasMaxLength(500);
                entity.Property(e => e.ImageUrl).HasMaxLength(255);
                entity.HasOne(e => e.Owner)
                      .WithMany(u => u.OwnedServers)
                      .HasForeignKey(e => e.OwnerId)
                      .OnDelete(DeleteBehavior.Restrict);
            });

            // Channel configuration
            builder.Entity<Channel>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).IsRequired().HasMaxLength(100);
                entity.Property(e => e.Description).HasMaxLength(500);
                entity.HasOne(e => e.Server)
                      .WithMany(s => s.Channels)
                      .HasForeignKey(e => e.ServerId)
                      .OnDelete(DeleteBehavior.Cascade);
            });

            // Message configuration
            builder.Entity<Message>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Content).IsRequired().HasMaxLength(2000);
                entity.HasOne(e => e.User)
                      .WithMany(u => u.Messages)
                      .HasForeignKey(e => e.UserId)
                      .OnDelete(DeleteBehavior.Restrict);
                entity.HasOne(e => e.Channel)
                      .WithMany(c => c.Messages)
                      .HasForeignKey(e => e.ChannelId)
                      .OnDelete(DeleteBehavior.Cascade);
            });

            // ServerMember configuration
            builder.Entity<ServerMember>(entity =>
            {
                entity.HasKey(e => new { e.UserId, e.ServerId });
                entity.HasOne(e => e.User)
                      .WithMany(u => u.ServerMemberships)
                      .HasForeignKey(e => e.UserId)
                      .OnDelete(DeleteBehavior.Cascade);
                entity.HasOne(e => e.Server)
                      .WithMany(s => s.Members)
                      .HasForeignKey(e => e.ServerId)
                      .OnDelete(DeleteBehavior.Cascade);
            });

            // DirectMessage configuration
            builder.Entity<DirectMessage>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Content).IsRequired().HasMaxLength(2000);
                entity.HasOne(e => e.Sender)
                      .WithMany(u => u.SentDirectMessages)
                      .HasForeignKey(e => e.SenderId)
                      .OnDelete(DeleteBehavior.Restrict);
                entity.HasOne(e => e.Receiver)
                      .WithMany(u => u.ReceivedDirectMessages)
                      .HasForeignKey(e => e.ReceiverId)
                      .OnDelete(DeleteBehavior.Restrict);
            });

            // UserProfile configuration
            builder.Entity<UserProfile>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.HasOne(e => e.User)
                      .WithOne(u => u.Profile)
                      .HasForeignKey<UserProfile>(e => e.UserId)
                      .OnDelete(DeleteBehavior.Cascade);
                entity.Property(e => e.DisplayName).HasMaxLength(100);
                entity.Property(e => e.Bio).HasMaxLength(500);
                entity.Property(e => e.AvatarUrl).HasMaxLength(255);
                entity.Property(e => e.ProfilePictureUrl).HasMaxLength(255);
            });

            // Friendship configuration
            builder.Entity<Friendship>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.HasOne(e => e.Requester)
                      .WithMany()
                      .HasForeignKey(e => e.RequesterId)
                      .OnDelete(DeleteBehavior.Restrict);
                entity.HasOne(e => e.Addressee)
                      .WithMany()
                      .HasForeignKey(e => e.AddresseeId)
                      .OnDelete(DeleteBehavior.Restrict);

                // Prevent duplicate friendship requests
                entity.HasIndex(e => new { e.RequesterId, e.AddresseeId })
                      .IsUnique();

                // Add check constraint to prevent self-friendship
                entity.ToTable(t => t.HasCheckConstraint("CK_Friendship_NoSelfFriend",
                    "[RequesterId] != [AddresseeId]"));
            });
        }
    }
}
