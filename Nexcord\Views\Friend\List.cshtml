@model Nexcord.ViewModels.FriendListViewModel
@{
    ViewData["Title"] = "Friends";
}

<div class="friends-container">
    <div class="friends-header">
        <div class="header-info">
            <h2><i class="fas fa-users"></i> Friends</h2>
            <div class="friends-stats">
                <span class="stat">
                    <i class="fas fa-circle online-dot"></i>
                    @Model.OnlineFriendsCount Online
                </span>
                <span class="stat-divider">•</span>
                <span class="stat">@Model.TotalFriendsCount Total Friends</span>
                @if (Model.PendingRequestsCount > 0)
                {
                    <span class="stat-divider">•</span>
                    <span class="stat pending">@Model.PendingRequestsCount Pending</span>
                }
            </div>
        </div>
        <div class="header-actions">
            <a href="@Url.Action("Search")" class="btn btn-primary">
                <i class="fas fa-user-plus"></i>
                Add Friend
            </a>
        </div>
    </div>

    <div class="friends-tabs">
        <button class="tab-btn active" data-tab="friends">
            <i class="fas fa-users"></i>
            All Friends (@Model.TotalFriendsCount)
        </button>
        @if (Model.PendingRequestsCount > 0)
        {
            <button class="tab-btn" data-tab="pending">
                <i class="fas fa-clock"></i>
                Pending (@Model.PendingRequestsCount)
            </button>
        }
        @if (Model.SentRequests.Any())
        {
            <button class="tab-btn" data-tab="sent">
                <i class="fas fa-paper-plane"></i>
                Sent (@Model.SentRequests.Count)
            </button>
        }
    </div>

    <div class="tab-content">
        <!-- Friends Tab -->
        <div class="tab-pane active" id="friends-tab">
            @if (Model.Friends.Any())
            {
                <div class="friends-list">
                    @foreach (var friend in Model.Friends)
                    {
                        <div class="friend-item" data-friend-id="@friend.Id">
                            <div class="friend-avatar">
                                @if (!string.IsNullOrEmpty(friend.AvatarUrl))
                                {
                                    <img src="@friend.AvatarUrl" alt="@friend.DisplayName" />
                                }
                                else
                                {
                                    <div class="avatar-placeholder">
                                        @friend.DisplayName.Substring(0, 1).ToUpper()
                                    </div>
                                }
                                @if (friend.IsOnline)
                                {
                                    <div class="status-indicator online"></div>
                                }
                            </div>

                            <div class="friend-info">
                                <div class="friend-name">@friend.DisplayName</div>
                                <div class="friend-status">
                                    @if (friend.IsOnline)
                                    {
                                        <span class="status online">Online</span>
                                    }
                                    else
                                    {
                                        <span class="status offline">Last seen @friend.LastActive.ToString("MMM dd, yyyy")</span>
                                    }
                                </div>
                            </div>

                            <div class="friend-actions">
                                <a href="@Url.Action("Chat", new { friendId = friend.Id })" class="action-btn message-btn" title="Message">
                                    <i class="fas fa-comment"></i>
                                </a>
                                <button class="action-btn more-btn" title="More" onclick="showFriendMenu('@friend.Id', this)">
                                    <i class="fas fa-ellipsis-h"></i>
                                </button>
                            </div>
                        </div>
                    }
                </div>
            }
            else
            {
                <div class="empty-state">
                    <i class="fas fa-user-friends"></i>
                    <h3>No friends yet</h3>
                    <p>Start building your network by adding friends!</p>
                    <a href="@Url.Action("Search")" class="btn btn-primary">
                        <i class="fas fa-user-plus"></i>
                        Find Friends
                    </a>
                </div>
            }
        </div>

        <!-- Pending Requests Tab -->
        @if (Model.PendingRequestsCount > 0)
        {
            <div class="tab-pane" id="pending-tab">
                <div class="requests-list">
                    @foreach (var request in Model.PendingRequests)
                    {
                        <div class="request-card" data-friendship-id="@request.FriendshipId">
                            <div class="request-avatar">
                                @if (!string.IsNullOrEmpty(request.AvatarUrl))
                                {
                                    <img src="@request.AvatarUrl" alt="@request.DisplayName" />
                                }
                                else
                                {
                                    <div class="avatar-placeholder">
                                        <i class="fas fa-user"></i>
                                    </div>
                                }
                                @if (request.IsOnline)
                                {
                                    <div class="status-indicator online"></div>
                                }
                            </div>

                            <div class="request-info">
                                <div class="request-name">
                                    <h4>@request.DisplayName</h4>
                                    <span class="username">@@@request.Username</span>
                                </div>
                                <div class="request-time">
                                    Sent @request.RequestedAt.ToString("MMM dd, yyyy 'at' h:mm tt")
                                </div>
                            </div>

                            <div class="request-actions">
                                <button class="btn btn-success btn-sm accept-request-btn" data-friendship-id="@request.FriendshipId">
                                    <i class="fas fa-check"></i>
                                    Accept
                                </button>
                                <button class="btn btn-danger btn-sm decline-request-btn" data-friendship-id="@request.FriendshipId">
                                    <i class="fas fa-times"></i>
                                    Decline
                                </button>
                            </div>
                        </div>
                    }
                </div>
            </div>
        }

        <!-- Sent Requests Tab -->
        @if (Model.SentRequests.Any())
        {
            <div class="tab-pane" id="sent-tab">
                <div class="requests-list">
                    @foreach (var request in Model.SentRequests)
                    {
                        <div class="request-card">
                            <div class="request-avatar">
                                @if (!string.IsNullOrEmpty(request.AvatarUrl))
                                {
                                    <img src="@request.AvatarUrl" alt="@request.DisplayName" />
                                }
                                else
                                {
                                    <div class="avatar-placeholder">
                                        <i class="fas fa-user"></i>
                                    </div>
                                }
                                @if (request.IsOnline)
                                {
                                    <div class="status-indicator online"></div>
                                }
                            </div>

                            <div class="request-info">
                                <div class="request-name">
                                    <h4>@request.DisplayName</h4>
                                    <span class="username">@@@request.Username</span>
                                </div>
                                <div class="request-time">
                                    Sent @request.RequestedAt.ToString("MMM dd, yyyy 'at' h:mm tt")
                                </div>
                            </div>

                            <div class="request-actions">
                                <span class="status-badge pending">
                                    <i class="fas fa-clock"></i>
                                    Pending
                                </span>
                            </div>
                        </div>
                    }
                </div>
            </div>
        }
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            // Tab switching
            $('.tab-btn').click(function() {
                const tabName = $(this).data('tab');
                
                // Update active tab button
                $('.tab-btn').removeClass('active');
                $(this).addClass('active');
                
                // Update active tab content
                $('.tab-pane').removeClass('active');
                $(`#${tabName}-tab`).addClass('active');
            });

            // Accept friend request
            $('.accept-request-btn').click(function() {
                const friendshipId = $(this).data('friendship-id');
                const button = $(this);
                const card = button.closest('.request-card');
                
                button.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Accepting...');
                
                $.ajax({
                    url: '@Url.Action("AcceptRequest")',
                    type: 'POST',
                    data: { friendshipId: friendshipId },
                    success: function(response) {
                        if (response.success) {
                            card.fadeOut(300, function() {
                                $(this).remove();
                                updatePendingCount();
                            });
                            showNotification(response.message, 'success');
                        } else {
                            button.prop('disabled', false).html('<i class="fas fa-check"></i> Accept');
                            showNotification(response.message, 'error');
                        }
                    },
                    error: function() {
                        button.prop('disabled', false).html('<i class="fas fa-check"></i> Accept');
                        showNotification('An error occurred. Please try again.', 'error');
                    }
                });
            });

            // Decline friend request
            $('.decline-request-btn').click(function() {
                const friendshipId = $(this).data('friendship-id');
                const button = $(this);
                const card = button.closest('.request-card');
                
                button.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Declining...');
                
                $.ajax({
                    url: '@Url.Action("DeclineRequest")',
                    type: 'POST',
                    data: { friendshipId: friendshipId },
                    success: function(response) {
                        if (response.success) {
                            card.fadeOut(300, function() {
                                $(this).remove();
                                updatePendingCount();
                            });
                            showNotification(response.message, 'success');
                        } else {
                            button.prop('disabled', false).html('<i class="fas fa-times"></i> Decline');
                            showNotification(response.message, 'error');
                        }
                    },
                    error: function() {
                        button.prop('disabled', false).html('<i class="fas fa-times"></i> Decline');
                        showNotification('An error occurred. Please try again.', 'error');
                    }
                });
            });

            // Close friend menu when clicking outside
            $(document).click(function(e) {
                if (!$(e.target).closest('.friend-menu, .more-btn').length) {
                    $('.friend-menu').remove();
                }
            });
        });

        function updatePendingCount() {
            const remainingRequests = $('.request-card').length;
            const pendingTab = $('[data-tab="pending"]');
            
            if (remainingRequests === 0) {
                pendingTab.hide();
                // Switch to friends tab if pending tab was active
                if (pendingTab.hasClass('active')) {
                    $('[data-tab="friends"]').click();
                }
            } else {
                pendingTab.find('.tab-count').text(`(${remainingRequests})`);
            }
        }

        function showFriendMenu(friendId, button) {
            // Remove any existing menus
            $('.friend-menu').remove();

            const menu = $(`
                <div class="friend-menu">
                    <div class="menu-item" onclick="removeFriend('${friendId}')">
                        <i class="fas fa-user-times"></i>
                        Remove Friend
                    </div>
                </div>
            `);

            const buttonRect = button.getBoundingClientRect();
            menu.css({
                position: 'fixed',
                top: buttonRect.bottom + 5,
                right: window.innerWidth - buttonRect.right,
                zIndex: 1000
            });

            $('body').append(menu);
        }

        function removeFriend(friendId) {
            $('.friend-menu').remove();

            if (!confirm('Are you sure you want to remove this friend?')) {
                return;
            }

            const friendItem = $(`.friend-item[data-friend-id="${friendId}"]`);

            $.ajax({
                url: '@Url.Action("RemoveFriend")',
                type: 'POST',
                data: { friendId: friendId },
                success: function(response) {
                    if (response.success) {
                        friendItem.fadeOut(300, function() {
                            $(this).remove();
                        });
                        showNotification(response.message, 'success');
                    } else {
                        showNotification(response.message, 'error');
                    }
                },
                error: function() {
                    showNotification('An error occurred. Please try again.', 'error');
                }
            });
        }

        function showNotification(message, type) {
            const notification = $(`
                <div class="notification ${type}">
                    <i class="fas ${type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle'}"></i>
                    ${message}
                </div>
            `);

            $('body').append(notification);

            setTimeout(() => notification.addClass('show'), 100);
            setTimeout(() => {
                notification.removeClass('show');
                setTimeout(() => notification.remove(), 300);
            }, 3000);
        }
    </script>
}

<style>
.friends-container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
}

.friends-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid var(--nexcord-dark-lighter);
}

.friends-header h2 {
    color: var(--nexcord-text);
    margin: 0 0 10px 0;
}

.friends-stats {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--nexcord-text-muted);
    font-size: 14px;
}

.online-dot {
    color: #43b581;
    font-size: 8px;
}

.stat.pending {
    color: #faa61a;
}

.friends-tabs {
    display: flex;
    gap: 8px;
    margin-bottom: 20px;
    border-bottom: 1px solid var(--nexcord-dark-lighter);
}

.tab-btn {
    padding: 12px 16px;
    background: none;
    border: none;
    color: var(--nexcord-text-muted);
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.tab-btn:hover {
    color: var(--nexcord-text);
}

.tab-btn.active {
    color: var(--nexcord-primary);
    border-bottom-color: var(--nexcord-primary);
}

.tab-content {
    min-height: 400px;
}

.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
}

.friends-list, .requests-list {
    display: flex;
    flex-direction: column;
    gap: 1px;
    border-radius: 8px;
    overflow: hidden;
}

.friend-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 16px;
    background: transparent;
    transition: background 0.15s ease;
    border-bottom: 1px solid rgba(255, 255, 255, 0.04);
    min-height: 62px;
}

.friend-item:hover {
    background: rgba(255, 255, 255, 0.04);
}

.friend-item:hover .friend-actions {
    opacity: 1;
}

.friend-avatar, .request-avatar {
    position: relative;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    overflow: hidden;
    flex-shrink: 0;
}

.friend-avatar img, .request-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.avatar-placeholder {
    width: 100%;
    height: 100%;
    background: var(--nexcord-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 14px;
    font-weight: 600;
}

.status-indicator {
    position: absolute;
    bottom: -2px;
    right: -2px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid var(--nexcord-dark);
}

.status-indicator.online {
    background: #43b581;
}

.friend-info, .request-info {
    flex: 1;
    min-width: 0;
}

.friend-name {
    color: var(--nexcord-text);
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 2px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.friend-status {
    font-size: 12px;
    color: var(--nexcord-text-muted);
}

.status.online {
    color: #43b581;
}

.status.offline {
    color: var(--nexcord-text-muted);
}

.friend-actions {
    display: flex;
    gap: 4px;
    opacity: 0;
    transition: opacity 0.15s ease;
}

.action-btn {
    width: 36px;
    height: 36px;
    border: none;
    background: var(--nexcord-dark-lighter);
    color: var(--nexcord-text-muted);
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.15s ease;
    text-decoration: none;
}

.action-btn:hover {
    background: var(--nexcord-dark-light);
    color: var(--nexcord-text);
}

.message-btn:hover {
    background: var(--nexcord-primary);
    color: white;
}

.request-card {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 16px;
    background: var(--nexcord-dark-lighter);
    border-radius: 8px;
    transition: background 0.2s ease;
}

.request-card:hover {
    background: rgba(255, 255, 255, 0.05);
}

.request-actions {
    display: flex;
    gap: 8px;
}

.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 6px;
    transition: all 0.2s ease;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 12px;
}

.btn-primary {
    background: var(--nexcord-primary);
    color: white;
}

.btn-primary:hover {
    background: var(--nexcord-primary-dark);
}

.btn-success {
    background: #43b581;
    color: white;
}

.btn-success:hover {
    background: #369870;
}

.btn-danger {
    background: #f04747;
    color: white;
}

.btn-danger:hover {
    background: #d73c3c;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.status-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 4px;
}

.status-badge.pending {
    background: rgba(250, 166, 26, 0.2);
    color: #faa61a;
}

.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: var(--nexcord-text-muted);
}

.empty-state i {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
}

.empty-state h3 {
    margin-bottom: 8px;
    color: var(--nexcord-text);
}

.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 20px;
    border-radius: 8px;
    color: white;
    font-weight: 600;
    z-index: 1000;
    transform: translateX(100%);
    transition: transform 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.notification.show {
    transform: translateX(0);
}

.notification.success {
    background: #43b581;
}

.notification.error {
    background: #f04747;
}

.friend-menu {
    background: var(--nexcord-dark-lighter);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 6px;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.24);
    min-width: 140px;
    overflow: hidden;
}

.menu-item {
    padding: 8px 12px;
    color: var(--nexcord-text);
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    transition: background 0.15s ease;
}

.menu-item:hover {
    background: var(--nexcord-primary);
    color: white;
}

.menu-item i {
    width: 16px;
    text-align: center;
}
</style>
