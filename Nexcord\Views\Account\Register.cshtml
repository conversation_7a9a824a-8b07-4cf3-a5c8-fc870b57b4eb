@model Nexcord.ViewModels.RegisterViewModel
@{
    ViewData["Title"] = "Sign Up - NEXCORD";
    Layout = "_Layout";
}

<div class="auth-container">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-6 col-md-8">
                <div class="auth-card">
                    <div class="auth-header">
                        <div class="auth-logo">
                            <i class="fas fa-user-plus"></i>
                        </div>
                        <h2 class="auth-title">Join NEXCORD</h2>
                        <p class="auth-subtitle">Create your gaming account today</p>
                    </div>

                    <form asp-action="Register" method="post" class="auth-form">
                        <div asp-validation-summary="All" class="text-danger mb-3"></div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="FirstName" class="form-label">
                                        <i class="fas fa-user"></i>
                                        First Name
                                    </label>
                                    <input asp-for="FirstName" class="form-control auth-input" placeholder="First name" />
                                    <span asp-validation-for="FirstName" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="LastName" class="form-label">
                                        <i class="fas fa-user"></i>
                                        Last Name
                                    </label>
                                    <input asp-for="LastName" class="form-control auth-input" placeholder="Last name" />
                                    <span asp-validation-for="LastName" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label asp-for="Username" class="form-label">
                                <i class="fas fa-at"></i>
                                Username
                            </label>
                            <input asp-for="Username" class="form-control auth-input" placeholder="Choose a unique username" />
                            <span asp-validation-for="Username" class="text-danger"></span>
                            <small class="form-text text-muted">Username can only contain letters, numbers, and underscores (3-30 characters)</small>
                        </div>

                        <div class="form-group">
                            <label asp-for="Email" class="form-label">
                                <i class="fas fa-envelope"></i>
                                Email Address
                            </label>
                            <input asp-for="Email" class="form-control auth-input" placeholder="Enter your email" />
                            <span asp-validation-for="Email" class="text-danger"></span>
                        </div>

                        <div class="form-group">
                            <label asp-for="Password" class="form-label">
                                <i class="fas fa-lock"></i>
                                Password
                            </label>
                            <input asp-for="Password" class="form-control auth-input" placeholder="Create a password" />
                            <span asp-validation-for="Password" class="text-danger"></span>
                        </div>

                        <div class="form-group">
                            <label asp-for="ConfirmPassword" class="form-label">
                                <i class="fas fa-lock"></i>
                                Confirm Password
                            </label>
                            <input asp-for="ConfirmPassword" class="form-control auth-input" placeholder="Confirm your password" />
                            <span asp-validation-for="ConfirmPassword" class="text-danger"></span>
                        </div>

                        <div class="form-group">
                            <div class="form-check">
                                <input asp-for="AgreeToTerms" class="form-check-input" />
                                <label asp-for="AgreeToTerms" class="form-check-label">
                                    I agree to the <a href="#" class="auth-link">Terms of Service</a> and <a href="#" class="auth-link">Privacy Policy</a>
                                </label>
                                <span asp-validation-for="AgreeToTerms" class="text-danger"></span>
                            </div>
                        </div>

                        <button type="submit" class="btn-auth btn-primary">
                            <i class="fas fa-rocket"></i>
                            Create Account
                        </button>
                    </form>

                    <div class="auth-footer">
                        <p>Already have an account? 
                            <a asp-action="Login" class="auth-link">Sign in here</a>
                        </p>
                    </div>

                    <div class="auth-divider">
                        <span>or continue with</span>
                    </div>

                    <div class="social-login">
                        <button class="btn-social btn-discord">
                            <i class="fab fa-discord"></i>
                            Discord
                        </button>
                        <button class="btn-social btn-steam">
                            <i class="fab fa-steam"></i>
                            Steam
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
