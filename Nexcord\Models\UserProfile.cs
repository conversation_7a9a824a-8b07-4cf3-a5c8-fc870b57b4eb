using System.ComponentModel.DataAnnotations;

namespace Nexcord.Models
{
    public class UserProfile
    {
        public int Id { get; set; }

        [Required]
        public string UserId { get; set; } = string.Empty;

        [StringLength(100)]
        public string? DisplayName { get; set; }

        [StringLength(500)]
        public string? Bio { get; set; }

        [StringLength(255)]
        public string? AvatarUrl { get; set; }

        [StringLength(255)]
        public string? ProfilePictureUrl { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        // Gaming preferences
        public string? FavoriteGames { get; set; } // JSON array of favorite games
        public string? GamingPlatforms { get; set; } // PC, Xbox, PlayStation, etc.
        public string? DiscordTag { get; set; }
        public string? SteamProfile { get; set; }

        // Theme preferences
        public string ThemeColor { get; set; } = "#7C3AED"; // Default purple
        public bool DarkMode { get; set; } = true;

        // Navigation properties
        public virtual ApplicationUser User { get; set; } = null!;
    }
}
