using System.ComponentModel.DataAnnotations;

namespace Nexcord.Models
{
    public enum MemberRole
    {
        Member = 0,
        Moderator = 1,
        Admin = 2,
        Owner = 3
    }

    public class ServerMember
    {
        [Required]
        public string UserId { get; set; } = string.Empty;

        [Required]
        public int ServerId { get; set; }

        public DateTime JoinedAt { get; set; } = DateTime.UtcNow;

        public MemberRole Role { get; set; } = MemberRole.Member;

        [StringLength(100)]
        public string? Nickname { get; set; }

        public bool IsMuted { get; set; } = false;
        public bool IsBanned { get; set; } = false;

        // Navigation properties
        public virtual ApplicationUser User { get; set; } = null!;
        public virtual Server Server { get; set; } = null!;
    }
}
