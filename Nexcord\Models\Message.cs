using System.ComponentModel.DataAnnotations;

namespace Nexcord.Models
{
    public class Message
    {
        public int Id { get; set; }

        [Required]
        [StringLength(2000)]
        public string Content { get; set; } = string.Empty;

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime? EditedAt { get; set; }

        [Required]
        public string UserId { get; set; } = string.Empty;

        [Required]
        public int ChannelId { get; set; }

        public bool IsDeleted { get; set; } = false;

        // Navigation properties
        public virtual ApplicationUser User { get; set; } = null!;
        public virtual Channel Channel { get; set; } = null!;
    }
}
