using System.ComponentModel.DataAnnotations;
using Nexcord.Models;

namespace Nexcord.ViewModels
{
    public class UserSearchViewModel
    {
        [Required]
        [StringLength(100, MinimumLength = 2)]
        public string SearchTerm { get; set; } = string.Empty;
        
        public List<UserSearchResultViewModel> Results { get; set; } = new List<UserSearchResultViewModel>();
        public bool HasSearched { get; set; } = false;
    }

    public class UserSearchResultViewModel
    {
        public string Id { get; set; } = string.Empty;
        public string Username { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string DisplayName { get; set; } = string.Empty;
        public string FullName { get; set; } = string.Empty;
        public string? AvatarUrl { get; set; }
        public bool IsOnline { get; set; }
        public DateTime LastActive { get; set; }
        
        // Friend status with current user
        public FriendStatus FriendStatus { get; set; }
        public int? FriendshipId { get; set; }
        public bool IsCurrentUser { get; set; }
    }

    public enum FriendStatus
    {
        NotFriend,
        PendingOutgoing,  // Current user sent request
        PendingIncoming,  // Current user received request
        Friends,
        Blocked
    }

    public class FriendListViewModel
    {
        public List<FriendViewModel> Friends { get; set; } = new List<FriendViewModel>();
        public List<FriendRequestViewModel> PendingRequests { get; set; } = new List<FriendRequestViewModel>();
        public List<FriendRequestViewModel> SentRequests { get; set; } = new List<FriendRequestViewModel>();
        public int OnlineFriendsCount { get; set; }
        public int TotalFriendsCount { get; set; }
        public int PendingRequestsCount { get; set; }
    }

    public class FriendViewModel
    {
        public string Id { get; set; } = string.Empty;
        public string Username { get; set; } = string.Empty;
        public string DisplayName { get; set; } = string.Empty;
        public string FullName { get; set; } = string.Empty;
        public string? AvatarUrl { get; set; }
        public bool IsOnline { get; set; }
        public DateTime LastActive { get; set; }
        public DateTime FriendsSince { get; set; }
        public int UnreadMessagesCount { get; set; }
        public string? LastMessageContent { get; set; }
        public DateTime? LastMessageTime { get; set; }
        public string? CurrentActivity { get; set; }
    }

    public class FriendRequestViewModel
    {
        public int FriendshipId { get; set; }
        public string UserId { get; set; } = string.Empty;
        public string Username { get; set; } = string.Empty;
        public string DisplayName { get; set; } = string.Empty;
        public string FullName { get; set; } = string.Empty;
        public string? AvatarUrl { get; set; }
        public bool IsOnline { get; set; }
        public DateTime RequestedAt { get; set; }
        public bool IsIncoming { get; set; } // true if current user received the request
        public string? Message { get; set; } // Optional message with friend request
    }

    public class SendFriendRequestViewModel
    {
        [Required]
        public string UserId { get; set; } = string.Empty;

        [StringLength(200)]
        public string? Message { get; set; }
    }

    public class SearchApiRequest
    {
        [Required]
        public string SearchTerm { get; set; } = string.Empty;
    }

    public class DirectMessageViewModel
    {
        public int Id { get; set; }
        public string Content { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public DateTime? EditedAt { get; set; }
        public bool IsFromCurrentUser { get; set; }
        public string SenderName { get; set; } = string.Empty;
        public string? SenderAvatarUrl { get; set; }
        public bool IsRead { get; set; }
    }

    public class ChatViewModel
    {
        public string FriendId { get; set; } = string.Empty;
        public string FriendUsername { get; set; } = string.Empty;
        public string FriendDisplayName { get; set; } = string.Empty;
        public string? FriendAvatarUrl { get; set; }
        public bool FriendIsOnline { get; set; }
        public DateTime FriendLastActive { get; set; }
        
        public List<DirectMessageViewModel> Messages { get; set; } = new List<DirectMessageViewModel>();
        public int TotalMessages { get; set; }
        public bool HasMoreMessages { get; set; }
        
        [Required]
        [StringLength(2000, MinimumLength = 1)]
        public string NewMessage { get; set; } = string.Empty;
    }

    public class FriendActivityViewModel
    {
        public string UserId { get; set; } = string.Empty;
        public string Username { get; set; } = string.Empty;
        public string DisplayName { get; set; } = string.Empty;
        public bool IsOnline { get; set; }
        public string Status { get; set; } = "Online"; // Online, Away, Busy, Invisible
        public string? CurrentActivity { get; set; }
        public string? GamePlaying { get; set; }
        public DateTime LastActive { get; set; }
    }

    public class DashboardViewModel
    {
        public ApplicationUser User { get; set; } = null!;
        public List<FriendViewModel> Friends { get; set; } = new List<FriendViewModel>();
        public int OnlineFriendsCount { get; set; }
        public int TotalFriendsCount { get; set; }
        public List<FriendRequestViewModel> PendingRequests { get; set; } = new List<FriendRequestViewModel>();
        public int PendingRequestsCount { get; set; }
        public List<DirectMessageViewModel> RecentMessages { get; set; } = new List<DirectMessageViewModel>();
    }
}
