{"Version": 1, "WorkspaceRootPath": "G:\\nexcord\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{BB1CA942-2910-47EC-B1F2-971565C72A6A}|Nexcord\\Nexcord.csproj|g:\\nexcord\\nexcord\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{BB1CA942-2910-47EC-B1F2-971565C72A6A}|Nexcord\\Nexcord.csproj|solutionrelative:nexcord\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{BB1CA942-2910-47EC-B1F2-971565C72A6A}|Nexcord\\Nexcord.csproj|g:\\nexcord\\nexcord\\controllers\\accountcontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{BB1CA942-2910-47EC-B1F2-971565C72A6A}|Nexcord\\Nexcord.csproj|solutionrelative:nexcord\\controllers\\accountcontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{BB1CA942-2910-47EC-B1F2-971565C72A6A}|Nexcord\\Nexcord.csproj|g:\\nexcord\\nexcord\\controllers\\friendcontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{BB1CA942-2910-47EC-B1F2-971565C72A6A}|Nexcord\\Nexcord.csproj|solutionrelative:nexcord\\controllers\\friendcontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{BB1CA942-2910-47EC-B1F2-971565C72A6A}|Nexcord\\Nexcord.csproj|g:\\nexcord\\nexcord\\views\\home\\index.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{BB1CA942-2910-47EC-B1F2-971565C72A6A}|Nexcord\\Nexcord.csproj|solutionrelative:nexcord\\views\\home\\index.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 2, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{aa2115a1-9712-457b-9047-dbb71ca2cdd2}"}, {"$type": "Bookmark", "Name": "ST:0:0:{cce594b6-0c39-4442-ba28-10c64ac7e89f}"}, {"$type": "Document", "DocumentIndex": 0, "Title": "appsettings.json", "DocumentMoniker": "G:\\nexcord\\Nexcord\\appsettings.json", "RelativeDocumentMoniker": "Nexcord\\appsettings.json", "ToolTip": "G:\\nexcord\\Nexcord\\appsettings.json", "RelativeToolTip": "Nexcord\\appsettings.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAwAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-07-24T08:59:35.227Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "FriendController.cs", "DocumentMoniker": "G:\\nexcord\\Nexcord\\Controllers\\FriendController.cs", "RelativeDocumentMoniker": "Nexcord\\Controllers\\FriendController.cs", "ToolTip": "G:\\nexcord\\Nexcord\\Controllers\\FriendController.cs", "RelativeToolTip": "Nexcord\\Controllers\\FriendController.cs", "ViewState": "AgIAACwAAAAAAAAAAAAwwDAAAAAwAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-19T19:30:06.351Z"}, {"$type": "Document", "DocumentIndex": 1, "Title": "AccountController.cs", "DocumentMoniker": "G:\\nexcord\\Nexcord\\Controllers\\AccountController.cs", "RelativeDocumentMoniker": "Nexcord\\Controllers\\AccountController.cs", "ToolTip": "G:\\nexcord\\Nexcord\\Controllers\\AccountController.cs", "RelativeToolTip": "Nexcord\\Controllers\\AccountController.cs", "ViewState": "AgIAABwAAAAAAAAAAAAkwCoAAAAQAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-19T19:30:05.629Z"}, {"$type": "Document", "DocumentIndex": 3, "Title": "Index.cshtml", "DocumentMoniker": "G:\\nexcord\\Nexcord\\Views\\Home\\Index.cshtml", "RelativeDocumentMoniker": "Nexcord\\Views\\Home\\Index.cshtml", "ToolTip": "G:\\nexcord\\Nexcord\\Views\\Home\\Index.cshtml", "RelativeToolTip": "Nexcord\\Views\\Home\\Index.cshtml", "ViewState": "AgIAAE4AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-07-19T19:29:33.962Z"}]}]}]}