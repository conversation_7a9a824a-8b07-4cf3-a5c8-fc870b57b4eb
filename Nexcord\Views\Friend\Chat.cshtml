@model Nexcord.ViewModels.ChatViewModel
@{
    ViewData["Title"] = $"Chat with {Model.FriendDisplayName}";
}

<div class="chat-container">
    <div class="chat-header">
        <div class="friend-info">
            <div class="friend-avatar">
                @if (!string.IsNullOrEmpty(Model.FriendAvatarUrl))
                {
                    <img src="@Model.FriendAvatarUrl" alt="@Model.FriendDisplayName" />
                }
                else
                {
                    <div class="avatar-placeholder">
                        <i class="fas fa-user"></i>
                    </div>
                }
                @if (Model.FriendIsOnline)
                {
                    <div class="status-indicator online"></div>
                }
            </div>
            <div class="friend-details">
                <h3>@Model.FriendDisplayName</h3>
                <span class="friend-status">
                    @if (Model.FriendIsOnline)
                    {
                        <i class="fas fa-circle online-dot"></i>
                        <span>Online</span>
                    }
                    else
                    {
                        <span>Last seen @Model.FriendLastActive.ToString("MMM dd, yyyy 'at' h:mm tt")</span>
                    }
                </span>
            </div>
        </div>
        <div class="chat-actions">
            <button class="btn btn-secondary" title="Voice Call">
                <i class="fas fa-phone"></i>
            </button>
            <button class="btn btn-secondary" title="Video Call">
                <i class="fas fa-video"></i>
            </button>
            <a href="@Url.Action("List")" class="btn btn-secondary" title="Back to Friends">
                <i class="fas fa-arrow-left"></i>
            </a>
        </div>
    </div>

    <div class="chat-messages" id="chat-messages">
        @if (Model.Messages.Any())
        {
            @foreach (var message in Model.Messages)
            {
                <div class="message @(message.IsFromCurrentUser ? "message-own" : "message-other")">
                    @if (!message.IsFromCurrentUser)
                    {
                        <div class="message-avatar">
                            @if (!string.IsNullOrEmpty(message.SenderAvatarUrl))
                            {
                                <img src="@message.SenderAvatarUrl" alt="@message.SenderName" />
                            }
                            else
                            {
                                <div class="avatar-placeholder-small">
                                    <i class="fas fa-user"></i>
                                </div>
                            }
                        </div>
                    }
                    <div class="message-content">
                        @if (!message.IsFromCurrentUser)
                        {
                            <div class="message-header">
                                <span class="message-sender">@message.SenderName</span>
                                <span class="message-time">@message.CreatedAt.ToString("MMM dd, yyyy 'at' h:mm tt")</span>
                            </div>
                        }
                        <div class="message-text">
                            @message.Content
                            @if (message.EditedAt.HasValue)
                            {
                                <span class="message-edited">(edited)</span>
                            }
                        </div>
                        @if (message.IsFromCurrentUser)
                        {
                            <div class="message-time-own">
                                @message.CreatedAt.ToString("h:mm tt")
                            </div>
                        }
                    </div>
                </div>
            }
        }
        else
        {
            <div class="no-messages">
                <i class="fas fa-comments"></i>
                <h3>Start your conversation</h3>
                <p>Send a message to @Model.FriendDisplayName to get started!</p>
            </div>
        }
    </div>

    <div class="chat-input-container">
        <form id="message-form" class="message-form">
            <div class="input-group">
                <input type="text" id="message-input" class="message-input" 
                       placeholder="Type a message..." autocomplete="off" maxlength="2000" />
                <button type="submit" class="send-btn" id="send-btn">
                    <i class="fas fa-paper-plane"></i>
                </button>
            </div>
        </form>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            const friendId = '@Model.FriendId';
            const currentUserId = '@User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value';
            const messageInput = $('#message-input');
            const messagesContainer = $('#chat-messages');
            const messageForm = $('#message-form');
            const sendBtn = $('#send-btn');

            // Initialize SignalR connection
            const connection = new signalR.HubConnectionBuilder()
                .withUrl("/chatHub")
                .build();

            // Start SignalR connection
            connection.start().then(function () {
                console.log('SignalR Connected');
            }).catch(function (err) {
                console.error('SignalR Connection Error: ', err.toString());
            });

            // Listen for incoming direct messages
            connection.on("ReceiveDirectMessage", function (messageData) {
                // Only show messages for this conversation
                if ((messageData.senderId === friendId && messageData.receiverId === currentUserId) ||
                    (messageData.senderId === currentUserId && messageData.receiverId === friendId)) {

                    const isFromCurrentUser = messageData.senderId === currentUserId;
                    addMessageToChat({
                        id: messageData.id,
                        content: messageData.content,
                        createdAt: messageData.createdAt,
                        isFromCurrentUser: isFromCurrentUser,
                        senderName: messageData.sender.fullName,
                        senderAvatarUrl: messageData.sender.avatarUrl
                    }, isFromCurrentUser);

                    scrollToBottom();
                }
            });

            // Auto-focus message input
            messageInput.focus();

            // Scroll to bottom of messages
            scrollToBottom();

            // Handle form submission
            messageForm.submit(function(e) {
                e.preventDefault();
                sendMessage();
            });

            // Handle Enter key
            messageInput.keypress(function(e) {
                if (e.which === 13 && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage();
                }
            });

            function sendMessage() {
                const content = messageInput.val().trim();

                if (!content) {
                    return;
                }

                // Disable input while sending
                messageInput.prop('disabled', true);
                sendBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i>');

                // Send message via SignalR
                connection.invoke("SendDirectMessage", friendId, content)
                    .then(function() {
                        // Clear input
                        messageInput.val('');

                        // Re-enable input
                        messageInput.prop('disabled', false).focus();
                        sendBtn.prop('disabled', false).html('<i class="fas fa-paper-plane"></i>');
                    })
                    .catch(function(err) {
                        console.error('Error sending message: ', err.toString());
                        showNotification('Failed to send message', 'error');

                        // Re-enable input
                        messageInput.prop('disabled', false).focus();
                        sendBtn.prop('disabled', false).html('<i class="fas fa-paper-plane"></i>');
                    });
            }

            function addMessageToChat(message, isOwn) {
                // Remove no-messages placeholder if it exists
                $('.no-messages').remove();

                const messageHtml = `
                    <div class="message ${isOwn ? 'message-own' : 'message-other'}">
                        ${!isOwn ? `
                            <div class="message-avatar">
                                ${message.senderAvatarUrl ? 
                                    `<img src="${message.senderAvatarUrl}" alt="${message.senderName}" />` :
                                    `<div class="avatar-placeholder-small"><i class="fas fa-user"></i></div>`
                                }
                            </div>
                        ` : ''}
                        <div class="message-content">
                            ${!isOwn ? `
                                <div class="message-header">
                                    <span class="message-sender">${message.senderName}</span>
                                    <span class="message-time">${formatMessageTime(message.createdAt)}</span>
                                </div>
                            ` : ''}
                            <div class="message-text">
                                ${message.content}
                            </div>
                            ${isOwn ? `
                                <div class="message-time-own">
                                    ${formatMessageTimeShort(message.createdAt)}
                                </div>
                            ` : ''}
                        </div>
                    </div>
                `;

                messagesContainer.append(messageHtml);
            }

            function scrollToBottom() {
                messagesContainer.scrollTop(messagesContainer[0].scrollHeight);
            }

            function formatMessageTime(dateString) {
                const date = new Date(dateString);
                return date.toLocaleDateString('en-US', { 
                    month: 'short', 
                    day: 'numeric', 
                    year: 'numeric',
                    hour: 'numeric',
                    minute: '2-digit',
                    hour12: true
                });
            }

            function formatMessageTimeShort(dateString) {
                const date = new Date(dateString);
                return date.toLocaleTimeString('en-US', { 
                    hour: 'numeric',
                    minute: '2-digit',
                    hour12: true
                });
            }

            function showNotification(message, type) {
                const notification = $(`
                    <div class="notification ${type}">
                        <i class="fas ${type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle'}"></i>
                        ${message}
                    </div>
                `);
                
                $('body').append(notification);
                
                setTimeout(() => notification.addClass('show'), 100);
                setTimeout(() => {
                    notification.removeClass('show');
                    setTimeout(() => notification.remove(), 300);
                }, 3000);
            }

            // Auto-resize input based on content
            messageInput.on('input', function() {
                this.style.height = 'auto';
                this.style.height = Math.min(this.scrollHeight, 120) + 'px';
            });
        });
    </script>
}

<style>
.chat-container {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 100px);
    max-width: 1000px;
    margin: 0 auto;
    background: var(--nexcord-dark);
    border-radius: 8px;
    overflow: hidden;
}

.chat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    background: var(--nexcord-dark-lighter);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.friend-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.friend-avatar {
    position: relative;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
}

.friend-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.avatar-placeholder {
    width: 100%;
    height: 100%;
    background: var(--nexcord-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 16px;
}

.status-indicator {
    position: absolute;
    bottom: -2px;
    right: -2px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid var(--nexcord-dark-lighter);
}

.status-indicator.online {
    background: #43b581;
}

.friend-details h3 {
    color: var(--nexcord-text);
    margin: 0;
    font-size: 18px;
    font-weight: 600;
}

.friend-status {
    display: flex;
    align-items: center;
    gap: 4px;
    color: var(--nexcord-text-muted);
    font-size: 12px;
    margin-top: 2px;
}

.online-dot {
    color: #43b581;
    font-size: 8px;
}

.chat-actions {
    display: flex;
    gap: 8px;
}

.chat-messages {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.message {
    display: flex;
    gap: 12px;
    max-width: 70%;
}

.message-own {
    align-self: flex-end;
    flex-direction: row-reverse;
}

.message-other {
    align-self: flex-start;
}

.message-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    overflow: hidden;
    flex-shrink: 0;
}

.message-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.avatar-placeholder-small {
    width: 100%;
    height: 100%;
    background: var(--nexcord-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 12px;
}

.message-content {
    flex: 1;
}

.message-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 4px;
}

.message-sender {
    color: var(--nexcord-text);
    font-weight: 600;
    font-size: 14px;
}

.message-time {
    color: var(--nexcord-text-muted);
    font-size: 11px;
}

.message-text {
    background: var(--nexcord-dark-lighter);
    padding: 12px 16px;
    border-radius: 18px;
    color: var(--nexcord-text);
    word-wrap: break-word;
    line-height: 1.4;
}

.message-own .message-text {
    background: var(--nexcord-primary);
    color: white;
}

.message-time-own {
    text-align: right;
    color: var(--nexcord-text-muted);
    font-size: 11px;
    margin-top: 4px;
}

.message-edited {
    color: var(--nexcord-text-muted);
    font-size: 11px;
    font-style: italic;
    margin-left: 8px;
}

.no-messages {
    text-align: center;
    padding: 60px 20px;
    color: var(--nexcord-text-muted);
    margin: auto;
}

.no-messages i {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
}

.no-messages h3 {
    margin-bottom: 8px;
    color: var(--nexcord-text);
}

.chat-input-container {
    padding: 20px;
    background: var(--nexcord-dark-lighter);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.input-group {
    display: flex;
    gap: 12px;
    align-items: flex-end;
}

.message-input {
    flex: 1;
    padding: 12px 16px;
    border: 2px solid transparent;
    border-radius: 20px;
    background: var(--nexcord-dark);
    color: var(--nexcord-text);
    font-size: 14px;
    resize: none;
    min-height: 44px;
    max-height: 120px;
    line-height: 1.4;
}

.message-input:focus {
    outline: none;
    border-color: var(--nexcord-primary);
}

.send-btn {
    width: 44px;
    height: 44px;
    border: none;
    border-radius: 50%;
    background: var(--nexcord-primary);
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.2s ease;
    flex-shrink: 0;
}

.send-btn:hover {
    background: var(--nexcord-primary-dark);
}

.send-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn {
    padding: 8px 12px;
    border: none;
    border-radius: 6px;
    background: var(--nexcord-dark);
    color: var(--nexcord-text);
    cursor: pointer;
    transition: background 0.2s ease;
    text-decoration: none;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn:hover {
    background: rgba(255, 255, 255, 0.1);
}

.btn-secondary {
    background: var(--nexcord-dark);
}

.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 20px;
    border-radius: 8px;
    color: white;
    font-weight: 600;
    z-index: 1000;
    transform: translateX(100%);
    transition: transform 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.notification.show {
    transform: translateX(0);
}

.notification.success {
    background: #43b581;
}

.notification.error {
    background: #f04747;
}

/* Scrollbar styling */
.chat-messages::-webkit-scrollbar {
    width: 8px;
}

.chat-messages::-webkit-scrollbar-track {
    background: var(--nexcord-dark);
}

.chat-messages::-webkit-scrollbar-thumb {
    background: var(--nexcord-dark-lighter);
    border-radius: 4px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.2);
}
</style>
