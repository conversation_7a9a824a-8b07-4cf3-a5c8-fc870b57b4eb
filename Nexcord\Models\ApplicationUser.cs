using Microsoft.AspNetCore.Identity;
using System.ComponentModel.DataAnnotations;

namespace Nexcord.Models
{
    public class ApplicationUser : IdentityUser
    {
        [Required]
        [StringLength(100)]
        public string FirstName { get; set; } = string.Empty;

        [Required]
        [StringLength(100)]
        public string LastName { get; set; } = string.Empty;

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime LastActive { get; set; } = DateTime.UtcNow;
        public bool IsOnline { get; set; } = false;

        // Navigation properties
        public virtual UserProfile? Profile { get; set; }
        public virtual ICollection<Server> OwnedServers { get; set; } = new List<Server>();
        public virtual ICollection<ServerMember> ServerMemberships { get; set; } = new List<ServerMember>();
        public virtual ICollection<Message> Messages { get; set; } = new List<Message>();
        public virtual ICollection<DirectMessage> SentDirectMessages { get; set; } = new List<DirectMessage>();
        public virtual ICollection<DirectMessage> ReceivedDirectMessages { get; set; } = new List<DirectMessage>();

        public string FullName => $"{FirstName} {LastName}";

        // Helper property to get display name (DisplayName from profile or FullName)
        public string DisplayName => Profile?.DisplayName ?? FullName;

        // Helper property to get username (uses Identity's UserName)
        public string Username => UserName ?? "";
    }
}
