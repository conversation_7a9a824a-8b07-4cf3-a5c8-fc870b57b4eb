/* _content/Nexcord/Views/Shared/_Layout.cshtml.rz.scp.css */
/* Please see documentation at https://learn.microsoft.com/aspnet/core/client-side/bundling-and-minification
for details on configuring this project to bundle and minify static web assets. */

a.navbar-brand[b-x98j1qy09w] {
  white-space: normal;
  text-align: center;
  word-break: break-all;
}

a[b-x98j1qy09w] {
  color: #0077cc;
}

.btn-primary[b-x98j1qy09w] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.nav-pills .nav-link.active[b-x98j1qy09w], .nav-pills .show > .nav-link[b-x98j1qy09w] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.border-top[b-x98j1qy09w] {
  border-top: 1px solid #e5e5e5;
}
.border-bottom[b-x98j1qy09w] {
  border-bottom: 1px solid #e5e5e5;
}

.box-shadow[b-x98j1qy09w] {
  box-shadow: 0 .25rem .75rem rgba(0, 0, 0, .05);
}

button.accept-policy[b-x98j1qy09w] {
  font-size: 1rem;
  line-height: inherit;
}


