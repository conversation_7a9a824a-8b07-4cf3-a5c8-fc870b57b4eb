@model Nexcord.ViewModels.DashboardViewModel
@{
    ViewData["Title"] = "Dashboard - NEXCORD";
    Layout = "_Layout";
}

<div class="discord-layout">
    <!-- Server List (Left Sidebar) -->
    <div class="server-list">
        <div class="server-item home-server active">
            <i class="fas fa-home"></i>
        </div>
        <div class="server-divider"></div>
        @if (Model.User.ServerMemberships.Any())
        {
            @foreach (var membership in Model.User.ServerMemberships)
            {
                <div class="server-item" data-server-id="@membership.ServerId">
                    @if (membership.Server.ImageUrl != null)
                    {
                        <img src="@membership.Server.ImageUrl" alt="@membership.Server.Name" />
                    }
                    else
                    {
                        <span>@membership.Server.Name.Substring(0, 1).ToUpper()</span>
                    }
                </div>
            }
        }
        <div class="server-item add-server">
            <i class="fas fa-plus"></i>
        </div>
    </div>

    <!-- Channel List (Second Sidebar) -->
    <div class="channel-list">
        <div class="server-header">
            <h3 id="server-name">Friends</h3>
            <i class="fas fa-chevron-down"></i>
        </div>

        <!-- Search Bar -->
        <div class="search-section">
            <div class="search-bar">
                <input type="text" placeholder="Find or start a conversation" id="friend-search">
                <i class="fas fa-search"></i>
            </div>
        </div>

        <!-- Navigation Items -->
        <div class="nav-section">
            <div class="nav-item active" data-nav="friends" onclick="showFriendsView()">
                <i class="fas fa-user-friends"></i>
                <span>Friends</span>
                @if (Model.PendingRequestsCount > 0)
                {
                    <span class="notification-badge">@Model.PendingRequestsCount</span>
                }
            </div>
            <div class="nav-item" data-nav="events" onclick="showEvents()">
                <i class="fas fa-calendar-alt"></i>
                <span>Events</span>
            </div>
        </div>

        <div class="channel-section">
            <div class="channel-category">
                <i class="fas fa-chevron-right"></i>
                <span>DIRECT MESSAGES</span>
            </div>
            <div class="dm-list">
                @if (Model.Friends.Any())
                {
                    @foreach (var friend in Model.Friends.Take(5))
                    {
                        <div class="dm-friend-item chat-btn" data-friend-id="@friend.Id" data-friend-name="@friend.Username" data-friend-avatar="@friend.AvatarUrl" data-friend-online="@friend.IsOnline.ToString().ToLower()" onclick="openChatSimple('@friend.Id', '@friend.Username', '@friend.AvatarUrl', @friend.IsOnline.ToString().ToLower())">
                            <div class="dm-friend-avatar">
                                @if (!string.IsNullOrEmpty(friend.AvatarUrl))
                                {
                                    <img src="@friend.AvatarUrl" alt="@friend.Username" />
                                }
                                else
                                {
                                    <div class="avatar-placeholder-small">
                                        <i class="fas fa-user"></i>
                                    </div>
                                }
                                @if (friend.IsOnline)
                                {
                                    <div class="status-indicator-small online"></div>
                                }
                                else
                                {
                                    <div class="status-indicator-small offline"></div>
                                }
                            </div>
                            <div class="dm-friend-info">
                                <div class="dm-friend-name">@friend.Username</div>
                                <div class="dm-friend-status">
                                    @if (friend.IsOnline)
                                    {
                                        <span>Online</span>
                                    }
                                    else
                                    {
                                        <span>Last seen @friend.LastActive.ToString("MMM dd")</span>
                                    }
                                </div>
                            </div>
                        </div>
                    }
                    @if (Model.Friends.Count > 5)
                    {
                        <a href="@Url.Action("List", "Friend")" class="channel-item">
                            <i class="fas fa-ellipsis-h"></i>
                            <span>View All Friends</span>
                        </a>
                    }
                }
                else
                {
                    <div class="channel-item">
                        <div class="user-avatar-small">
                            <i class="fas fa-user"></i>
                        </div>
                        <span>No friends yet</span>
                    </div>
                }
            </div>
        </div>

        <!-- User Panel -->
        <div class="user-panel">
            <div class="user-info">
                <div class="user-avatar">
                    @if (Model.User.Profile?.AvatarUrl != null)
                    {
                        <img src="@Model.User.Profile.AvatarUrl" alt="Avatar" />
                    }
                    else
                    {
                        <i class="fas fa-user"></i>
                    }
                    <div class="status-indicator online"></div>
                </div>
                <div class="user-details">
                    <div class="username">@Model.User.Username</div>
                    <div class="user-tag">#@(Model.User.Username?.Substring(0, Math.Min(4, Model.User.Username?.Length ?? 0)) ?? "0000")</div>
                </div>
            </div>
            <div class="user-controls">
                <button class="control-btn" title="Mute">
                    <i class="fas fa-microphone"></i>
                </button>
                <button class="control-btn" title="Deafen">
                    <i class="fas fa-headphones"></i>
                </button>
                <button class="control-btn" title="Settings">
                    <i class="fas fa-cog"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- Main Chat Area -->
    <div class="chat-area">
        <div class="chat-header">
            <div class="channel-info" id="header-info">
                <i class="fas fa-user-friends" id="header-icon"></i>
                <span class="channel-name" id="header-title">Friends</span>
                <span class="friend-status" id="friend-status" style="display: none;"></span>
            </div>
            <div class="chat-controls">
                <button class="control-btn" id="back-to-friends" style="display: none;" title="Back to Friends" onclick="backToFriends()">
                    <i class="fas fa-arrow-left"></i>
                </button>
                <button class="control-btn" title="Start Voice Call">
                    <i class="fas fa-phone"></i>
                </button>
                <button class="control-btn" title="Start Video Call">
                    <i class="fas fa-video"></i>
                </button>
                <button class="control-btn" title="Search">
                    <i class="fas fa-search"></i>
                </button>
                <button class="control-btn" title="Member List">
                    <i class="fas fa-users"></i>
                </button>
            </div>
        </div>

        <div class="chat-content">
            <!-- Friends Content (Default View) -->
            <div class="friends-content" id="friends-view">
                <div class="friends-header">
                    <div class="friends-tabs">
                        <button class="friends-tab active" data-tab="online">Online — @Model.OnlineFriendsCount</button>
                        <button class="friends-tab" data-tab="all">All — @Model.TotalFriendsCount</button>
                        <button class="friends-tab" data-tab="pending">Pending — @Model.PendingRequestsCount</button>
                        <button class="friends-tab" data-tab="blocked">Blocked — 0</button>
                        <button class="friends-tab" data-tab="add-friend">Add Friend</button>
                    </div>
                </div>

                <div class="friends-list-container">
                    <!-- Online Friends Tab -->
                    <div class="tab-content active" id="online-content">
                        @if (Model.Friends.Where(f => f.IsOnline).Any())
                        {
                            <div class="friends-list">
                                @foreach (var friend in Model.Friends.Where(f => f.IsOnline))
                                {
                                    <div class="friend-item" onclick="openChatSimple('@friend.Id', '@friend.Username', '@friend.AvatarUrl', @friend.IsOnline.ToString().ToLower())">
                                        <div class="friend-avatar">
                                            @if (!string.IsNullOrEmpty(friend.AvatarUrl))
                                            {
                                                <img src="@friend.AvatarUrl" alt="@friend.Username" />
                                            }
                                            else
                                            {
                                                <div class="avatar-placeholder">
                                                    <i class="fas fa-user"></i>
                                                </div>
                                            }
                                            <div class="status-indicator online"></div>
                                        </div>
                                        <div class="friend-info">
                                            <div class="friend-name">@friend.Username</div>
                                            <div class="friend-status">Online</div>
                                        </div>
                                        <div class="friend-actions">
                                            <button class="action-btn chat-btn" data-friend-id="@friend.Id" data-friend-name="@friend.Username" data-friend-avatar="@friend.AvatarUrl" data-friend-online="@friend.IsOnline.ToString().ToLower()" title="Message">
                                                <i class="fas fa-comment"></i>
                                            </button>
                                            <button class="action-btn more-btn" title="More">
                                                <i class="fas fa-ellipsis-v"></i>
                                            </button>
                                        </div>
                                    </div>
                                }
                            </div>
                        }
                        else
                        {
                            <div class="empty-state">
                                <div class="empty-icon">
                                    <i class="fas fa-user-friends"></i>
                                </div>
                                <h3>No one's around to play with Wumpus</h3>
                                <p>None of your friends are online right now.</p>
                            </div>
                        }
                    </div>

                    <!-- All Friends Tab -->
                    <div class="tab-content" id="all-content">
                        @if (Model.Friends.Any())
                        {
                            <div class="friends-list">
                                @foreach (var friend in Model.Friends)
                                {
                                    <div class="friend-item" onclick="openChatSimple('@friend.Id', '@friend.Username', '@friend.AvatarUrl', @friend.IsOnline.ToString().ToLower())">
                                        <div class="friend-avatar">
                                            @if (!string.IsNullOrEmpty(friend.AvatarUrl))
                                            {
                                                <img src="@friend.AvatarUrl" alt="@friend.Username" />
                                            }
                                            else
                                            {
                                                <div class="avatar-placeholder">
                                                    <i class="fas fa-user"></i>
                                                </div>
                                            }
                                            @if (friend.IsOnline)
                                            {
                                                <div class="status-indicator online"></div>
                                            }
                                            else
                                            {
                                                <div class="status-indicator offline"></div>
                                            }
                                        </div>
                                        <div class="friend-info">
                                            <div class="friend-name">@friend.Username</div>
                                            <div class="friend-status">
                                                @if (friend.IsOnline)
                                                {
                                                    <span>Online</span>
                                                }
                                                else
                                                {
                                                    <span>Last seen @friend.LastActive.ToString("MMM dd, yyyy")</span>
                                                }
                                            </div>
                                        </div>
                                        <div class="friend-actions">
                                            <button class="action-btn chat-btn" data-friend-id="@friend.Id" data-friend-name="@friend.Username" data-friend-avatar="@friend.AvatarUrl" data-friend-online="@friend.IsOnline.ToString().ToLower()" title="Message">
                                                <i class="fas fa-comment"></i>
                                            </button>
                                            <button class="action-btn more-btn" title="More">
                                                <i class="fas fa-ellipsis-v"></i>
                                            </button>
                                        </div>
                                    </div>
                                }
                            </div>
                            <div class="view-all-friends">
                                <a href="@Url.Action("List", "Friend")" class="btn btn-primary">
                                    <i class="fas fa-users"></i>
                                    View All Friends
                                </a>
                            </div>
                        }
                        else
                        {
                            <div class="empty-state">
                                <div class="empty-icon">
                                    <i class="fas fa-user-friends"></i>
                                </div>
                                <h3>No friends yet</h3>
                                <p>Start building your network by adding friends!</p>
                                <a href="@Url.Action("Search", "Friend")" class="btn btn-primary">
                                    <i class="fas fa-user-plus"></i>
                                    Find Friends
                                </a>
                            </div>
                        }
                    </div>

                    <!-- Pending Requests Tab -->
                    <div class="tab-content" id="pending-content">
                        @if (Model.PendingRequests.Any())
                        {
                            <div class="friends-list">
                                @foreach (var request in Model.PendingRequests)
                                {
                                    <div class="friend-item pending-request" data-friendship-id="@request.FriendshipId">
                                        <div class="friend-avatar">
                                            @if (!string.IsNullOrEmpty(request.AvatarUrl))
                                            {
                                                <img src="@request.AvatarUrl" alt="@request.DisplayName" />
                                            }
                                            else
                                            {
                                                <div class="avatar-placeholder">
                                                    <i class="fas fa-user"></i>
                                                </div>
                                            }
                                            @if (request.IsOnline)
                                            {
                                                <div class="status-indicator online"></div>
                                            }
                                        </div>
                                        <div class="friend-info">
                                            <div class="friend-name">@request.Username</div>
                                            <div class="friend-status">Incoming Friend Request</div>
                                        </div>
                                        <div class="friend-actions">
                                            <button class="action-btn accept-btn" data-friendship-id="@request.FriendshipId" title="Accept">
                                                <i class="fas fa-check"></i>
                                            </button>
                                            <button class="action-btn decline-btn" data-friendship-id="@request.FriendshipId" title="Decline">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                    </div>
                                }
                            </div>
                            <div class="view-all-friends">
                                <a href="@Url.Action("List", "Friend")" class="btn btn-primary">
                                    <i class="fas fa-clock"></i>
                                    View All Requests
                                </a>
                            </div>
                        }
                        else
                        {
                            <div class="no-friends">
                                <div class="no-friends-icon">
                                    <i class="fas fa-clock"></i>
                                </div>
                                <h3>No pending friend requests</h3>
                                <p>Here you'll see friend requests waiting for your response.</p>
                            </div>
                        }
                    </div>

                    <!-- Blocked Tab -->
                    <div class="tab-content" id="blocked-content">
                        <div class="empty-state">
                            <div class="empty-icon">
                                <i class="fas fa-ban"></i>
                            </div>
                            <h3>You can't unblock the Wumpus</h3>
                            <p>No blocked users to show.</p>
                        </div>
                    </div>

                    <!-- Add Friend Tab -->
                    <div class="tab-content" id="add-friend-content">
                        <div class="add-friend-container">
                            <div class="add-friend-header">
                                <h3>Add Friend</h3>
                                <p>You can add friends with their NEXCORD username or email address.</p>
                            </div>

                            <form class="add-friend-form" id="add-friend-form">
                                @Html.AntiForgeryToken()
                                <div class="form-group">
                                    <div class="search-input-container">
                                        <input type="text"
                                               id="friend-search-input"
                                               placeholder="Enter a username or email address"
                                               class="form-control friend-search-input"
                                               autocomplete="off" />
                                        <button type="submit" class="btn btn-primary search-btn">
                                            <i class="fas fa-search"></i>
                                            Search
                                        </button>
                                    </div>
                                    <div class="search-help">
                                        <small class="text-muted">
                                            <i class="fas fa-info-circle"></i>
                                            You can search by username (e.g., "gamer123") or email address
                                        </small>
                                    </div>
                                </div>
                            </form>

                            <div class="search-results" id="friend-search-results" style="display: none;">
                                <h4>Search Results</h4>
                                <div class="results-list" id="results-list">
                                    <!-- Search results will be populated here -->
                                </div>
                            </div>

                            <div class="add-friend-suggestions">
                                <h4>Quick Add</h4>
                                <p class="suggestions-subtitle">People you might know</p>
                                <div class="suggestions-list">
                                    <div class="suggestion-item">
                                        <div class="suggestion-avatar">
                                            <i class="fas fa-user"></i>
                                        </div>
                                        <div class="suggestion-info">
                                            <div class="suggestion-name">Suggested Friend 1</div>
                                            <div class="suggestion-mutual">2 mutual friends</div>
                                        </div>
                                        <button class="btn btn-sm btn-primary">Add Friend</button>
                                    </div>
                                    <div class="suggestion-item">
                                        <div class="suggestion-avatar">
                                            <i class="fas fa-user"></i>
                                        </div>
                                        <div class="suggestion-info">
                                            <div class="suggestion-name">Suggested Friend 2</div>
                                            <div class="suggestion-mutual">1 mutual friend</div>
                                        </div>
                                        <button class="btn btn-sm btn-primary">Add Friend</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Events Content (Hidden by default) -->
            <div class="events-content" id="events-view" style="display: none;">
                <div class="events-header">
                    <div class="events-title">
                        <h2><i class="fas fa-calendar-alt"></i> Gaming Events</h2>
                        <p>Discover and join gaming events in your community</p>
                    </div>
                    <div class="events-actions">
                        <button class="btn btn-primary">
                            <i class="fas fa-plus"></i>
                            Create Event
                        </button>
                    </div>
                </div>

                <div class="events-tabs">
                    <button class="events-tab active" data-tab="upcoming">Upcoming Events</button>
                    <button class="events-tab" data-tab="my-events">My Events</button>
                    <button class="events-tab" data-tab="past">Past Events</button>
                </div>

                <div class="events-list-container">
                    <!-- Upcoming Events Tab -->
                    <div class="tab-content active" id="upcoming-content">
                        <div class="events-list">
                            <!-- Sample Event Cards -->
                            <div class="event-card">
                                <div class="event-image">
                                    <img src="https://via.placeholder.com/300x150/7289da/ffffff?text=Gaming+Tournament" alt="Gaming Tournament" />
                                    <div class="event-date">
                                        <span class="month">JAN</span>
                                        <span class="day">25</span>
                                    </div>
                                </div>
                                <div class="event-info">
                                    <h3>Weekly Gaming Tournament</h3>
                                    <p class="event-description">Join our weekly competitive gaming tournament featuring multiple games and prizes!</p>
                                    <div class="event-details">
                                        <span class="event-time"><i class="fas fa-clock"></i> 7:00 PM EST</span>
                                        <span class="event-participants"><i class="fas fa-users"></i> 24 participants</span>
                                    </div>
                                    <div class="event-actions">
                                        <button class="btn btn-primary btn-sm">Join Event</button>
                                        <button class="btn btn-secondary btn-sm">Learn More</button>
                                    </div>
                                </div>
                            </div>

                            <div class="event-card">
                                <div class="event-image">
                                    <img src="https://via.placeholder.com/300x150/43b581/ffffff?text=Community+Meetup" alt="Community Meetup" />
                                    <div class="event-date">
                                        <span class="month">JAN</span>
                                        <span class="day">28</span>
                                    </div>
                                </div>
                                <div class="event-info">
                                    <h3>Community Game Night</h3>
                                    <p class="event-description">Casual gaming session with the community. All skill levels welcome!</p>
                                    <div class="event-details">
                                        <span class="event-time"><i class="fas fa-clock"></i> 8:00 PM EST</span>
                                        <span class="event-participants"><i class="fas fa-users"></i> 12 participants</span>
                                    </div>
                                    <div class="event-actions">
                                        <button class="btn btn-primary btn-sm">Join Event</button>
                                        <button class="btn btn-secondary btn-sm">Learn More</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- My Events Tab -->
                    <div class="tab-content" id="my-events-content" style="display: none;">
                        <div class="empty-state">
                            <div class="empty-icon">
                                <i class="fas fa-calendar-plus"></i>
                            </div>
                            <h3>No events created yet</h3>
                            <p>Create your first gaming event and invite your friends!</p>
                            <button class="btn btn-primary">
                                <i class="fas fa-plus"></i>
                                Create Your First Event
                            </button>
                        </div>
                    </div>

                    <!-- Past Events Tab -->
                    <div class="tab-content" id="past-content" style="display: none;">
                        <div class="empty-state">
                            <div class="empty-icon">
                                <i class="fas fa-history"></i>
                            </div>
                            <h3>No past events</h3>
                            <p>Your event history will appear here.</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Chat View (Hidden by default) -->
            <div class="chat-view" id="chat-view" style="display: none;">
                <div class="chat-messages-container">
                    <div class="chat-messages" id="chat-messages">
                        <!-- Messages will be loaded here -->
                    </div>
                </div>
                <div class="chat-input-container">
                    <form id="message-form" class="message-form">
                        <div class="message-input-wrapper">
                            <input type="text" id="message-input" placeholder="Message..." autocomplete="off" />
                            <button type="submit" id="send-btn" class="send-button">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Member List (Right Sidebar) -->
    <div class="member-list">
        <div class="member-section">
            <div class="member-category">
                <span>ONLINE — @(Model.OnlineFriendsCount + 1)</span>
            </div>
            <!-- Current User -->
            <div class="member-item">
                <div class="member-avatar">
                    @if (Model.User.Profile?.AvatarUrl != null)
                    {
                        <img src="@Model.User.Profile.AvatarUrl" alt="Avatar" />
                    }
                    else
                    {
                        <i class="fas fa-user"></i>
                    }
                    <div class="status-indicator online"></div>
                </div>
                <div class="member-info">
                    <div class="member-name">@Model.User.Username</div>
                    <div class="member-activity">Online</div>
                </div>
            </div>
            <!-- Online Friends -->
            @foreach (var friend in Model.Friends.Where(f => f.IsOnline).Take(10))
            {
                <div class="member-item">
                    <div class="member-avatar">
                        @if (!string.IsNullOrEmpty(friend.AvatarUrl))
                        {
                            <img src="@friend.AvatarUrl" alt="@friend.Username" />
                        }
                        else
                        {
                            <i class="fas fa-user"></i>
                        }
                        <div class="status-indicator online"></div>
                    </div>
                    <div class="member-info">
                        <div class="member-name">@friend.Username</div>
                        <div class="member-activity">Online</div>
                    </div>
                </div>
            }
        </div>
    </div>
</div>

@section Scripts {
    <!-- SEPARATE ISOLATED SCRIPT FOR TABS ONLY -->
    <script>
        // This script runs completely independently
        (function() {
            console.log('=== ISOLATED TAB SCRIPT LOADING ===');

            function initializeTabs() {
                console.log('=== INITIALIZING TABS ===');

                // Find all tab elements
                const tabs = document.querySelectorAll('[data-tab]');
                console.log('Found tabs:', tabs.length);

                tabs.forEach(function(tab, index) {
                    console.log('Tab ' + index + ':', tab.getAttribute('data-tab'), tab);

                    tab.addEventListener('click', function(e) {
                        e.preventDefault();
                        console.log('=== TAB CLICKED ===', this.getAttribute('data-tab'));

                        const tabName = this.getAttribute('data-tab');

                        // Remove active from all tabs
                        document.querySelectorAll('[data-tab]').forEach(function(t) {
                            t.classList.remove('active');
                        });

                        // Add active to clicked tab
                        this.classList.add('active');

                        // Hide all content
                        document.querySelectorAll('.tab-content').forEach(function(content) {
                            content.style.display = 'none';
                        });

                        // Show target content
                        const targetContent = document.getElementById(tabName + '-content');
                        if (targetContent) {
                            targetContent.style.display = 'block';
                            console.log('=== SHOWED CONTENT ===', tabName + '-content');
                        } else {
                            console.error('=== CONTENT NOT FOUND ===', tabName + '-content');
                        }
                    });
                });
            }

            // Initialize when DOM is ready
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', initializeTabs);
            } else {
                initializeTabs();
            }
        })();
    </script>

    <script>
        // ORIGINAL SCRIPT (may have errors but won't affect tabs)
        console.log('=== ORIGINAL SCRIPT LOADING ===');

        // Dashboard functionality
        document.addEventListener('DOMContentLoaded', function() {
            console.log('=== DASHBOARD JAVASCRIPT STARTED ===');

            // BASIC TEST - Add click to ALL buttons on page
            document.addEventListener('click', function(e) {
                console.log('=== SOMETHING WAS CLICKED ===', e.target);
                if (e.target.getAttribute('data-tab') === 'pending') {
                    console.log('=== PENDING TAB DETECTED VIA GLOBAL CLICK ===');
                }
            });
            // Initialize navigation items
            console.log('=== INITIALIZING NAVIGATION ITEMS ===');
            const navItems = document.querySelectorAll('.nav-item');
            console.log('Found nav items:', navItems.length);

            navItems.forEach((item, index) => {
                console.log(`Nav item ${index}:`, item, 'data-nav:', item.getAttribute('data-nav'));
                item.addEventListener('click', function(e) {
                    e.preventDefault();
                    const nav = this.getAttribute('data-nav');
                    console.log('🔥 NAVIGATION CLICKED:', nav);
                    alert('Navigation clicked: ' + nav);

                    // Remove active class from all nav items
                    document.querySelectorAll('.nav-item').forEach(c => c.classList.remove('active'));

                    // Add active class to clicked item
                    this.classList.add('active');

                    // Handle different navigation
                    if (nav === 'friends') {
                        console.log('=== FRIENDS TAB CLICKED ===');

                        // First, ensure we're in friends view (not chat view)
                        const friendsView = document.getElementById('friends-view');
                        const chatView = document.getElementById('chat-view');

                        console.log('Friends view element:', friendsView);
                        console.log('Chat view element:', chatView);

                        if (friendsView && chatView) {
                            friendsView.style.display = 'block';
                            chatView.style.display = 'none';
                            console.log('✅ Switched to friends view');

                            // Reset header
                            const headerTitle = document.getElementById('header-title');
                            const backButton = document.getElementById('back-to-friends');

                            if (headerTitle) {
                                headerTitle.textContent = 'Friends';
                                console.log('✅ Header updated to Friends');
                            }
                            if (backButton) {
                                backButton.style.display = 'none';
                                console.log('✅ Back button hidden');
                            }
                        } else {
                            console.error('❌ Friends or chat view not found');
                        }

                        // Show all friends tab by default
                        document.querySelectorAll('[data-tab]').forEach(tab => tab.classList.remove('active'));
                        const allTab = document.querySelector('[data-tab="all"]');
                        if (allTab) {
                            allTab.classList.add('active');
                            console.log('✅ All tab activated');
                        } else {
                            console.error('❌ All tab not found');
                        }

                        document.querySelectorAll('.tab-content').forEach(content => {
                            content.style.display = 'none';
                            content.classList.remove('active');
                        });

                        const allContent = document.getElementById('all-content');
                        if (allContent) {
                            allContent.style.display = 'block';
                            allContent.classList.add('active');
                            console.log('✅ All friends content shown');
                        } else {
                            console.error('❌ All content not found');
                        }
                    } else if (nav === 'events') {
                        console.log('Events navigation clicked');
                        showEvents();
                    }
                });
            });

            // Initialize events tabs
            initializeEventsTabs();

            // Initialize add friend functionality
            initializeAddFriend();

            // Initialize friend search
            const friendSearch = document.getElementById('friend-search');
            if (friendSearch) {
                friendSearch.addEventListener('input', function(e) {
                    const searchTerm = e.target.value.toLowerCase();
                    console.log('Searching for:', searchTerm);

                    // Filter friends in Direct Messages
                    const dmFriends = document.querySelectorAll('.dm-friend-item');
                    dmFriends.forEach(friend => {
                        const friendName = friend.querySelector('.dm-friend-name')?.textContent.toLowerCase() || '';
                        if (friendName.includes(searchTerm)) {
                            friend.style.display = 'flex';
                        } else {
                            friend.style.display = 'none';
                        }
                    });
                });
            }

            // Initialize SignalR connection (with error handling)
            let connection = null;
            if (typeof signalR !== 'undefined') {
                connection = new signalR.HubConnectionBuilder()
                    .withUrl("/chatHub")
                    .build();
            } else {
                console.warn('SignalR not loaded, real-time features disabled');
            }

            // Start SignalR connection
            if (connection) {
                connection.start().then(function () {
                    console.log('SignalR Connected');
                }).catch(function (err) {
                    console.error('SignalR Connection Error: ', err.toString());
                });
            }

            // Listen for friend request notifications
            if (connection) {
                connection.on("FriendRequestReceived", function (requestData) {
                    showNotification(`${requestData.requester.displayName} sent you a friend request!`, 'info');

                    // Update pending requests count
                    updatePendingRequestsCount(1);

                    // Play notification sound (optional)
                    playNotificationSound();
                });

                // Listen for friend request accepted notifications
                connection.on("FriendRequestAccepted", function (friendData) {
                    showNotification(`${friendData.friend.username} accepted your friend request!`, 'success');

                    // Update friends count
                    updateFriendsCount(1);
                });

                // Listen for friend status changes
                connection.on("FriendStatusChanged", function (statusData) {
                    updateFriendStatus(statusData.userId, statusData.isOnline, statusData.lastActive);
                });
            }

            // Listen for direct messages
            if (connection) {
                connection.on("ReceiveDirectMessage", function (messageData) {
                    // Show notification for new messages (only if not in chat with that user)
                    const currentPath = window.location.pathname;
                    const isInChatWithSender = currentPath.includes(`/Friend/Chat/${messageData.senderId}`);

                    if (!isInChatWithSender) {
                        showNotification(`New message from ${messageData.sender.fullName}`, 'info');
                        playNotificationSound();
                    }
                });
            }
            // Server selection
            document.querySelectorAll('.server-item').forEach(item => {
                item.addEventListener('click', function() {
                    document.querySelectorAll('.server-item').forEach(s => s.classList.remove('active'));
                    this.classList.add('active');
                });
            });

            // Channel selection
            document.querySelectorAll('.channel-item').forEach(item => {
                item.addEventListener('click', function() {
                    document.querySelectorAll('.channel-item').forEach(c => c.classList.remove('active'));
                    this.classList.add('active');
                });
            });

            // Friends tabs - Simplified approach
            console.log('=== SETTING UP FRIENDS TABS ===');

            // Add click handlers to each tab individually
            const onlineTab = document.querySelector('[data-tab="online"]');
            const allTab = document.querySelector('[data-tab="all"]');
            const pendingTab = document.querySelector('[data-tab="pending"]');
            const blockedTab = document.querySelector('[data-tab="blocked"]');

            console.log('Tabs found:', {
                online: !!onlineTab,
                all: !!allTab,
                pending: !!pendingTab,
                blocked: !!blockedTab
            });

            console.log('Pending tab element:', pendingTab);
            console.log('Pending tab text:', pendingTab ? pendingTab.textContent : 'NULL');

            function showTab(tabName) {
                console.log('=== SHOW TAB FUNCTION CALLED ===');
                console.log('Tab name:', tabName);

                // Remove active from all tabs
                document.querySelectorAll('.friends-tab').forEach(t => t.classList.remove('active'));

                // Add active to clicked tab
                const clickedTab = document.querySelector(`[data-tab="${tabName}"]`);
                if (clickedTab) {
                    clickedTab.classList.add('active');
                }

                // Hide all content
                document.querySelectorAll('.tab-content').forEach(content => {
                    content.style.display = 'none';
                    content.classList.remove('active');
                });

                // Show target content
                const targetContent = document.getElementById(tabName + '-content');
                if (targetContent) {
                    targetContent.style.display = 'block';
                    targetContent.classList.add('active');
                    console.log('Successfully showed:', tabName + '-content');
                } else {
                    console.error('Content not found:', tabName + '-content');
                }
            }

            // Add individual click handlers
            if (onlineTab) {
                onlineTab.addEventListener('click', (e) => {
                    e.preventDefault();
                    showTab('online');
                });
            }

            if (allTab) {
                allTab.addEventListener('click', (e) => {
                    e.preventDefault();
                    showTab('all');
                });
            }

            if (pendingTab) {
                console.log('=== ADDING CLICK HANDLER TO PENDING TAB ===');
                pendingTab.addEventListener('click', (e) => {
                    e.preventDefault();
                    console.log('=== PENDING TAB CLICKED! ===');
                    console.log('Event:', e);
                    console.log('Target:', e.target);
                    showTab('pending');
                });
                console.log('=== PENDING TAB CLICK HANDLER ADDED ===');
            } else {
                console.error('=== PENDING TAB NOT FOUND! ===');
            }

            if (blockedTab) {
                blockedTab.addEventListener('click', (e) => {
                    e.preventDefault();
                    showTab('blocked');
                });
            }

            // Accept friend request
            document.querySelectorAll('.accept-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const friendshipId = this.getAttribute('data-friendship-id');
                    const requestItem = this.closest('.pending-request');

                    this.disabled = true;
                    this.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';

                    fetch('@Url.Action("AcceptRequest", "Friend")', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                        },
                        body: 'friendshipId=' + friendshipId
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            requestItem.style.opacity = '0.5';
                            requestItem.style.pointerEvents = 'none';
                            showNotification(data.message, 'success');
                            // Refresh page after a short delay
                            setTimeout(() => location.reload(), 1500);
                        } else {
                            this.disabled = false;
                            this.innerHTML = '<i class="fas fa-check"></i>';
                            showNotification(data.message, 'error');
                        }
                    })
                    .catch(error => {
                        this.disabled = false;
                        this.innerHTML = '<i class="fas fa-check"></i>';
                        showNotification('An error occurred', 'error');
                    });
                });
            });

            // Decline friend request
            document.querySelectorAll('.decline-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const friendshipId = this.getAttribute('data-friendship-id');
                    const requestItem = this.closest('.pending-request');

                    this.disabled = true;
                    this.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';

                    fetch('@Url.Action("DeclineRequest", "Friend")', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                        },
                        body: 'friendshipId=' + friendshipId
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            requestItem.style.opacity = '0.5';
                            requestItem.style.pointerEvents = 'none';
                            showNotification(data.message, 'success');
                            // Refresh page after a short delay
                            setTimeout(() => location.reload(), 1500);
                        } else {
                            this.disabled = false;
                            this.innerHTML = '<i class="fas fa-times"></i>';
                            showNotification(data.message, 'error');
                        }
                    })
                    .catch(error => {
                        this.disabled = false;
                        this.innerHTML = '<i class="fas fa-times"></i>';
                        showNotification('An error occurred', 'error');
                    });
                });
            });

            // Chat functionality
            let currentChatFriendId = null;
            let currentChatConnection = null;

            // Initialize chat buttons
            function initializeChatButtons() {
                console.log('=== INITIALIZING CHAT BUTTONS ===');
                const chatButtons = document.querySelectorAll('.chat-btn');
                console.log('Found chat buttons:', chatButtons.length);

                chatButtons.forEach((btn, index) => {
                    console.log(`Chat button ${index}:`, btn);
                    btn.addEventListener('click', function(e) {
                        e.preventDefault();
                        console.log('=== CHAT BUTTON CLICKED ===');
                        const friendId = this.getAttribute('data-friend-id');
                        const friendName = this.getAttribute('data-friend-name');
                        const friendAvatar = this.getAttribute('data-friend-avatar');
                        const friendOnline = this.getAttribute('data-friend-online') === 'true';

                        console.log('Friend data:', { friendId, friendName, friendAvatar, friendOnline });
                        openChat(friendId, friendName, friendAvatar, friendOnline);
                    });
                });
            }

            // Back to friends button
            document.getElementById('back-to-friends').addEventListener('click', function() {
                closeChat();
            });

            function openChat(friendId, friendName, friendAvatar, friendOnline) {
                console.log('=== OPENING CHAT ===');
                console.log('Friend ID:', friendId);
                console.log('Friend Name:', friendName);

                currentChatFriendId = friendId;

                // Check if elements exist
                const friendsView = document.getElementById('friends-view');
                const chatView = document.getElementById('chat-view');
                console.log('Friends view element:', friendsView);
                console.log('Chat view element:', chatView);

                if (!friendsView || !chatView) {
                    console.error('Required elements not found!');
                    return;
                }

                // Hide friends view, show chat view
                friendsView.style.display = 'none';
                chatView.style.display = 'flex';

                // Update header
                const headerIcon = document.getElementById('header-icon');
                const headerTitle = document.getElementById('header-title');
                const friendStatus = document.getElementById('friend-status');
                const backButton = document.getElementById('back-to-friends');

                if (headerIcon) headerIcon.className = 'fas fa-user';
                if (headerTitle) headerTitle.textContent = friendName;
                if (friendStatus) {
                    friendStatus.textContent = friendOnline ? 'Online' : 'Offline';
                    friendStatus.style.display = 'block';
                }
                if (backButton) backButton.style.display = 'block';

                console.log('Chat view should now be visible');

                // Load chat messages
                loadChatMessages(friendId);

                // Setup message form
                setupMessageForm(friendId);
            }

            function closeChat() {
                currentChatFriendId = null;

                // Show friends view, hide chat view
                document.getElementById('friends-view').style.display = 'block';
                document.getElementById('chat-view').style.display = 'none';

                // Reset header
                document.getElementById('header-icon').className = 'fas fa-user-friends';
                document.getElementById('header-title').textContent = 'Friends';
                document.getElementById('friend-status').style.display = 'none';
                document.getElementById('back-to-friends').style.display = 'none';

                // Clear chat messages
                document.getElementById('chat-messages').innerHTML = '';
            }

            function loadChatMessages(friendId) {
                fetch(`/Friend/GetChatMessages?friendId=${friendId}`)
                    .then(response => response.json())
                    .then(data => {
                        const messagesContainer = document.getElementById('chat-messages');
                        messagesContainer.innerHTML = '';

                        if (data.success && data.messages) {
                            data.messages.forEach(message => {
                                addMessageToChat(message, message.isFromCurrentUser);
                            });
                            scrollToBottom();
                        }
                    })
                    .catch(error => {
                        console.error('Error loading messages:', error);
                    });
            }

            function setupMessageForm(friendId) {
                const messageForm = document.getElementById('message-form');
                const messageInput = document.getElementById('message-input');
                const sendBtn = document.getElementById('send-btn');

                // Remove existing listeners
                messageForm.replaceWith(messageForm.cloneNode(true));
                const newForm = document.getElementById('message-form');
                const newInput = document.getElementById('message-input');
                const newSendBtn = document.getElementById('send-btn');

                newForm.addEventListener('submit', function(e) {
                    e.preventDefault();
                    const content = newInput.value.trim();
                    if (!content) return;

                    // Disable input while sending
                    newInput.disabled = true;
                    newSendBtn.disabled = true;
                    newSendBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';

                    // Send message via SignalR
                    if (connection) {
                        connection.invoke("SendDirectMessage", friendId, content)
                            .then(function() {
                                newInput.value = '';
                                newInput.disabled = false;
                                newInput.focus();
                                newSendBtn.disabled = false;
                                newSendBtn.innerHTML = '<i class="fas fa-paper-plane"></i>';
                            })
                            .catch(function(err) {
                                console.error('Error sending message:', err);
                                showNotification('Failed to send message', 'error');
                                newInput.disabled = false;
                                newSendBtn.disabled = false;
                                newSendBtn.innerHTML = '<i class="fas fa-paper-plane"></i>';
                            });
                    }
                });
            }

            function addMessageToChat(message, isOwn) {
                const messagesContainer = document.getElementById('chat-messages');
                const messageHtml = `
                    <div class="message ${isOwn ? 'message-own' : 'message-other'}">
                        ${!isOwn ? `
                            <div class="message-avatar">
                                ${message.senderAvatarUrl ?
                                    `<img src="${message.senderAvatarUrl}" alt="${message.senderName}" />` :
                                    `<div class="avatar-placeholder-small"><i class="fas fa-user"></i></div>`
                                }
                            </div>
                        ` : ''}
                        <div class="message-content">
                            ${!isOwn ? `
                                <div class="message-header">
                                    <span class="message-sender">${message.senderName}</span>
                                    <span class="message-time">${formatMessageTime(message.createdAt)}</span>
                                </div>
                            ` : ''}
                            <div class="message-text">
                                ${message.content}
                            </div>
                            ${isOwn ? `
                                <div class="message-time-own">
                                    ${formatMessageTimeShort(message.createdAt)}
                                </div>
                            ` : ''}
                        </div>
                    </div>
                `;
                messagesContainer.insertAdjacentHTML('beforeend', messageHtml);
            }

            function scrollToBottom() {
                const messagesContainer = document.getElementById('chat-messages');
                messagesContainer.scrollTop = messagesContainer.scrollHeight;
            }

            function formatMessageTime(dateString) {
                const date = new Date(dateString);
                return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
            }

            function formatMessageTimeShort(dateString) {
                const date = new Date(dateString);
                return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
            }

            // Initialize chat buttons
            initializeChatButtons();

            // Test button for debugging
            const testBtn = document.getElementById('test-chat-btn');
            if (testBtn) {
                testBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    console.log('=== TEST BUTTON CLICKED ===');
                    alert('Test button works! Now testing chat...');
                    openChat('test-id', 'Test Friend', '', true);
                });
            }

            // Listen for direct messages in chat
            if (connection) {
                connection.on("ReceiveDirectMessage", function (messageData) {
                    // Only show messages for current chat
                    if (currentChatFriendId &&
                        ((messageData.senderId === currentChatFriendId && messageData.receiverId === '@User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value') ||
                         (messageData.senderId === '@User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value' && messageData.receiverId === currentChatFriendId))) {

                        const isFromCurrentUser = messageData.senderId === '@User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value';
                        addMessageToChat({
                            id: messageData.id,
                            content: messageData.content,
                            createdAt: messageData.createdAt,
                            isFromCurrentUser: isFromCurrentUser,
                            senderName: messageData.sender.fullName,
                            senderAvatarUrl: messageData.sender.avatarUrl
                        }, isFromCurrentUser);

                        scrollToBottom();
                    }
                });
            }

            console.log('Dashboard loaded for user: @Model.User.UserName');
        });

        function showNotification(message, type) {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.innerHTML = `
                <i class="fas ${type === 'success' ? 'fa-check-circle' : type === 'info' ? 'fa-info-circle' : 'fa-exclamation-circle'}"></i>
                ${message}
            `;

            document.body.appendChild(notification);

            setTimeout(() => notification.classList.add('show'), 100);
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => notification.remove(), 300);
            }, 4000);
        }

        function updatePendingRequestsCount(increment) {
            const pendingTab = document.querySelector('[data-tab="pending"]');
            const pendingBadge = document.querySelector('[data-channel="pending"] .notification-badge');

            if (pendingTab && pendingBadge) {
                const currentCount = parseInt(pendingTab.textContent.match(/\d+/)[0]) || 0;
                const newCount = currentCount + increment;

                pendingTab.textContent = `Pending — ${newCount}`;
                pendingBadge.textContent = newCount;

                if (newCount > 0) {
                    pendingBadge.style.display = 'inline';
                } else {
                    pendingBadge.style.display = 'none';
                }
            }
        }

        function updateFriendsCount(increment) {
            const allTab = document.querySelector('[data-tab="all"]');
            const friendsBadge = document.querySelector('[data-channel="friends"] .notification-badge');

            if (allTab) {
                const currentCount = parseInt(allTab.textContent.match(/\d+/)[0]) || 0;
                const newCount = currentCount + increment;

                allTab.textContent = `All — ${newCount}`;

                if (friendsBadge) {
                    friendsBadge.textContent = newCount;
                    if (newCount > 0) {
                        friendsBadge.style.display = 'inline';
                    } else {
                        friendsBadge.style.display = 'none';
                    }
                }
            }
        }

        function updateFriendStatus(userId, isOnline, lastActive) {
            // Update friend status indicators in the UI
            const friendElements = document.querySelectorAll(`[data-friend-id="${userId}"]`);
            friendElements.forEach(element => {
                const statusIndicator = element.querySelector('.status-indicator');
                const statusText = element.querySelector('.friend-status, .member-activity');

                if (statusIndicator) {
                    if (isOnline) {
                        statusIndicator.classList.add('online');
                    } else {
                        statusIndicator.classList.remove('online');
                    }
                }

                if (statusText) {
                    statusText.textContent = isOnline ? 'Online' : `Last seen ${new Date(lastActive).toLocaleDateString()}`;
                }
            });
        }

        }); // End of DOMContentLoaded event listener

        // Simple chat function that can be called directly
        function openChatSimple(friendId, friendName, friendAvatar, friendOnline) {
            console.log('=== SIMPLE CHAT FUNCTION CALLED ===');
            console.log('Parameters:', { friendId, friendName, friendAvatar, friendOnline });

            // Hide friends view, show chat view
            const friendsView = document.getElementById('friends-view');
            const chatView = document.getElementById('chat-view');

            if (friendsView && chatView) {
                friendsView.style.display = 'none';
                chatView.style.display = 'flex';

                // Update header
                const headerTitle = document.getElementById('header-title');
                const backButton = document.getElementById('back-to-friends');

                if (headerTitle) headerTitle.textContent = friendName;
                if (backButton) backButton.style.display = 'block';

                console.log('Chat should now be visible');
            } else {
                console.error('Required elements not found');
            }
        }

        function playNotificationSound() {
            // Create a subtle notification sound
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);

            oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
            oscillator.frequency.setValueAtTime(600, audioContext.currentTime + 0.1);

            gainNode.gain.setValueAtTime(0, audioContext.currentTime);
            gainNode.gain.linearRampToValueAtTime(0.1, audioContext.currentTime + 0.01);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2);

            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + 0.2);
        }
    </script>

    <!-- Simple Chat Test Script -->
    <script>
        function openChatSimple(friendId, friendName, friendAvatar, friendOnline) {
            console.log('=== OPENING CHAT FOR FRIEND ===', friendName);

            try {
                const friendsView = document.getElementById('friends-view');
                const chatView = document.getElementById('chat-view');

                if (friendsView && chatView) {
                    friendsView.style.display = 'none';
                    chatView.style.display = 'flex';

                    const headerTitle = document.getElementById('header-title');
                    const backButton = document.getElementById('back-to-friends');

                    if (headerTitle) headerTitle.textContent = friendName;
                    if (backButton) backButton.style.display = 'block';

                    console.log('Opened chat with:', friendName);
                }
            } catch (error) {
                console.error('Error opening chat:', error);
            }
        }

        function backToFriends() {
            console.log('=== BACK TO FRIENDS CLICKED ===');

            try {
                const friendsView = document.getElementById('friends-view');
                const chatView = document.getElementById('chat-view');

                if (friendsView && chatView) {
                    friendsView.style.display = 'block';
                    chatView.style.display = 'none';

                    const headerTitle = document.getElementById('header-title');
                    const backButton = document.getElementById('back-to-friends');

                    if (headerTitle) headerTitle.textContent = 'Friends';
                    if (backButton) backButton.style.display = 'none';

                    console.log('Returned to friends view');
                }
            } catch (error) {
                console.error('Error returning to friends:', error);
            }
        }
    </script>

    <style>
        /* Search Bar Styles */
        .search-section {
            padding: 16px 8px;
            border-bottom: 1px solid #3c3c41;
            margin-bottom: 8px;
        }

        .search-bar {
            position: relative;
            background-color: #1e1f22;
            border-radius: 4px;
            padding: 8px 12px;
            display: flex;
            align-items: center;
        }

        .search-bar input {
            background: none;
            border: none;
            color: #dcddde;
            font-size: 14px;
            width: 100%;
            outline: none;
        }

        .search-bar input::placeholder {
            color: #6d6f78;
        }

        .search-bar i {
            color: #6d6f78;
            margin-left: 8px;
        }

        /* Navigation Items Styles */
        .nav-section {
            padding: 0 8px;
        }

        .nav-item {
            display: flex;
            align-items: center;
            padding: 8px 12px;
            margin: 2px 0;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.2s;
            color: #b9bbbe;
            text-decoration: none;
            position: relative;
        }

        .nav-item:hover {
            background-color: #3c3c41;
            color: #dcddde;
        }

        .nav-item.active {
            background-color: #5865f2;
            color: white;
        }

        .nav-item i {
            margin-right: 12px;
            width: 20px;
            font-size: 16px;
        }

        .nav-item span {
            font-weight: 500;
        }
    </style>

    <!-- Global Navigation Functions -->
    <script>
        // Initialize add friend functionality
        function initializeAddFriend() {
            console.log('🔧 Initializing add friend functionality...');

            const addFriendForm = document.getElementById('add-friend-form');
            const searchInput = document.getElementById('friend-search-input');
            const searchResults = document.getElementById('friend-search-results');
            const resultsList = document.getElementById('results-list');

            if (addFriendForm && searchInput) {
                addFriendForm.addEventListener('submit', async function(e) {
                    e.preventDefault();
                    e.stopPropagation();

                    console.log('🔥 Add friend form submitted!');

                    const searchTerm = searchInput.value.trim();
                    if (!searchTerm) {
                        showErrorMessage('Please enter a username or email address');
                        return;
                    }

                    console.log('🔍 Searching for:', searchTerm);

                    // Make sure we're in the add-friend tab
                    const addFriendTab = document.querySelector('[data-tab="add-friend"]');
                    if (addFriendTab && !addFriendTab.classList.contains('active')) {
                        addFriendTab.click(); // Switch to add-friend tab
                    }

                    try {
                        // Show loading state
                        resultsList.innerHTML = '<div class="loading-state"><i class="fas fa-spinner fa-spin"></i> Searching...</div>';
                        searchResults.style.display = 'block';

                        // Get anti-forgery token
                        const token = document.querySelector('input[name="__RequestVerificationToken"]')?.value;

                        // Make API call to search for users
                        const headers = {
                            'Content-Type': 'application/json'
                        };

                        if (token) {
                            headers['RequestVerificationToken'] = token;
                        }

                        const response = await fetch('/Friend/SearchApi', {
                            method: 'POST',
                            headers: headers,
                            body: JSON.stringify({ searchTerm: searchTerm })
                        });

                        if (response.ok) {
                            const users = await response.json();
                            if (users && users.length > 0) {
                                displaySearchResults(users);
                            } else {
                                resultsList.innerHTML = '<div class="empty-state"><i class="fas fa-user-slash"></i> No users found with that email or username</div>';
                                searchResults.style.display = 'block';
                            }
                        } else {
                            throw new Error('Search failed');
                        }
                    } catch (error) {
                        console.error('Search error:', error);
                        resultsList.innerHTML = '<div class="error-state"><i class="fas fa-exclamation-triangle"></i> Search failed. Please try again.</div>';
                    }
                });

                // Clear results when input is cleared
                searchInput.addEventListener('input', function() {
                    if (!this.value.trim()) {
                        searchResults.style.display = 'none';
                    }
                });
            }

            console.log('✅ Add friend functionality initialized');
        }

        function displaySearchResults(users) {
            const resultsList = document.getElementById('results-list');

            if (!users || users.length === 0) {
                resultsList.innerHTML = '<div class="empty-state"><i class="fas fa-user-slash"></i> No users found</div>';
                return;
            }

            resultsList.innerHTML = users.map(user => `
                <div class="search-result-item">
                    <div class="result-avatar">
                        ${user.avatarUrl ?
                            `<img src="${user.avatarUrl}" alt="${user.username}" />` :
                            '<i class="fas fa-user"></i>'
                        }
                    </div>
                    <div class="result-info">
                        <div class="result-name">${user.username}</div>
                        <div class="result-email">${user.email}</div>
                        <div class="result-status">
                            <i class="fas fa-circle ${user.isOnline ? 'online' : 'offline'}"></i>
                            ${user.isOnline ? 'Online' : 'Offline'}
                        </div>
                    </div>
                    <div class="result-actions">
                        ${user.friendStatus === 'NotFriend' ?
                            `<button class="btn btn-sm btn-primary" onclick="sendFriendRequest('${user.id}')">
                                <i class="fas fa-paper-plane"></i> Send Request
                            </button>` :
                            user.friendStatus === 'PendingOutgoing' ?
                            '<span class="status-badge pending"><i class="fas fa-clock"></i> Request Sent</span>' :
                            user.friendStatus === 'PendingIncoming' ?
                            '<span class="status-badge incoming"><i class="fas fa-user-clock"></i> Pending Request</span>' :
                            user.friendStatus === 'Friends' ?
                            '<span class="status-badge friends"><i class="fas fa-check"></i> Friends</span>' :
                            '<span class="status-badge">-</span>'
                        }
                    </div>
                </div>
            `).join('');
        }

        async function sendFriendRequest(userId) {
            try {
                // Find the button that was clicked and show loading state
                const button = document.querySelector(`button[onclick="sendFriendRequest('${userId}')"]`);
                if (button) {
                    button.disabled = true;
                    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Sending...';
                }

                // Get anti-forgery token
                const token = document.querySelector('input[name="__RequestVerificationToken"]')?.value;

                const headers = {
                    'Content-Type': 'application/json'
                };

                if (token) {
                    headers['RequestVerificationToken'] = token;
                }

                const response = await fetch('/Friend/SendRequest', {
                    method: 'POST',
                    headers: headers,
                    body: JSON.stringify({ userId: userId })
                });

                const result = await response.json();

                if (response.ok && result.success) {
                    // Show success message
                    showSuccessMessage('Request sent successfully!');

                    // Update the button to show request sent
                    if (button) {
                        button.innerHTML = '<i class="fas fa-check"></i> Request Sent';
                        button.classList.remove('btn-primary');
                        button.classList.add('btn-secondary');
                        button.disabled = true;
                    }
                } else {
                    throw new Error(result.message || 'Failed to send friend request');
                }
            } catch (error) {
                console.error('Friend request error:', error);
                showErrorMessage(error.message || 'Failed to send friend request. Please try again.');

                // Reset button state
                const button = document.querySelector(`button[onclick="sendFriendRequest('${userId}')"]`);
                if (button) {
                    button.disabled = false;
                    button.innerHTML = '<i class="fas fa-paper-plane"></i> Send Request';
                }
            }
        }

        function showSuccessMessage(message) {
            // Create or update success message element
            let messageEl = document.getElementById('friend-request-message');
            if (!messageEl) {
                messageEl = document.createElement('div');
                messageEl.id = 'friend-request-message';
                messageEl.className = 'alert alert-success';
                const searchResults = document.getElementById('friend-search-results');
                searchResults.parentNode.insertBefore(messageEl, searchResults);
            }

            messageEl.className = 'alert alert-success';
            messageEl.innerHTML = `<i class="fas fa-check-circle"></i> ${message}`;
            messageEl.style.display = 'block';

            // Hide after 5 seconds
            setTimeout(() => {
                messageEl.style.display = 'none';
            }, 5000);
        }

        function showErrorMessage(message) {
            // Create or update error message element
            let messageEl = document.getElementById('friend-request-message');
            if (!messageEl) {
                messageEl = document.createElement('div');
                messageEl.id = 'friend-request-message';
                messageEl.className = 'alert alert-danger';
                const searchResults = document.getElementById('friend-search-results');
                searchResults.parentNode.insertBefore(messageEl, searchResults);
            }

            messageEl.className = 'alert alert-danger';
            messageEl.innerHTML = `<i class="fas fa-exclamation-circle"></i> ${message}`;
            messageEl.style.display = 'block';

            // Hide after 5 seconds
            setTimeout(() => {
                messageEl.style.display = 'none';
            }, 5000);
        }

        // Initialize events tabs functionality
        function initializeEventsTabs() {
            console.log('🔧 Initializing events tabs...');

            document.querySelectorAll('.events-tab').forEach(tab => {
                tab.addEventListener('click', function(e) {
                    e.preventDefault();
                    const tabName = this.getAttribute('data-tab');
                    console.log('📋 Events tab clicked:', tabName);

                    // Remove active class from all events tabs
                    document.querySelectorAll('.events-tab').forEach(t => t.classList.remove('active'));

                    // Add active class to clicked tab
                    this.classList.add('active');

                    // Hide all events content
                    document.querySelectorAll('#events-view .tab-content').forEach(content => {
                        content.style.display = 'none';
                        content.classList.remove('active');
                    });

                    // Show selected content
                    const targetContent = document.getElementById(tabName + '-content');
                    if (targetContent) {
                        targetContent.style.display = 'block';
                        targetContent.classList.add('active');
                        console.log('✅ Showing events content for:', tabName);
                    }
                });
            });

            console.log('✅ Events tab handlers initialized');
        }

        // Global functions for navigation - must be accessible from onclick handlers
        function showFriendsView() {
            console.log('=== SHOW FRIENDS VIEW CALLED ===');

            // Remove active class from all nav items
            document.querySelectorAll('.nav-item').forEach(c => c.classList.remove('active'));

            // Add active class to friends nav item
            const friendsNavItem = document.querySelector('[data-nav="friends"]');
            if (friendsNavItem) friendsNavItem.classList.add('active');

            // Switch to friends view
            const friendsView = document.getElementById('friends-view');
            const chatView = document.getElementById('chat-view');
            const eventsView = document.getElementById('events-view');

            if (friendsView && chatView && eventsView) {
                friendsView.style.display = 'block';
                chatView.style.display = 'none';
                eventsView.style.display = 'none';
                console.log('✅ Switched to friends view');

                // Reset header
                const headerTitle = document.getElementById('header-title');
                const backButton = document.getElementById('back-to-friends');

                if (headerTitle) headerTitle.textContent = 'Friends';
                if (backButton) backButton.style.display = 'none';

                // Show all friends tab by default
                document.querySelectorAll('[data-tab]').forEach(tab => tab.classList.remove('active'));
                const allTab = document.querySelector('[data-tab="all"]');
                if (allTab) allTab.classList.add('active');

                document.querySelectorAll('.tab-content').forEach(content => {
                    content.style.display = 'none';
                    content.classList.remove('active');
                });

                const allContent = document.getElementById('all-content');
                if (allContent) {
                    allContent.style.display = 'block';
                    allContent.classList.add('active');
                    console.log('✅ All friends content shown');
                }
            } else {
                console.error('❌ Friends, chat, or events view not found');
            }
        }

        function showEvents() {
            console.log('=== SHOW EVENTS VIEW CALLED ===');

            // Remove active class from all nav items
            document.querySelectorAll('.nav-item').forEach(c => c.classList.remove('active'));

            // Add active class to events nav item
            const eventsNavItem = document.querySelector('[data-nav="events"]');
            if (eventsNavItem) eventsNavItem.classList.add('active');

            // Switch to events view
            const eventsView = document.getElementById('events-view');
            const friendsView = document.getElementById('friends-view');
            const chatView = document.getElementById('chat-view');

            if (eventsView && friendsView && chatView) {
                // Hide other views
                friendsView.style.display = 'none';
                chatView.style.display = 'none';

                // Show events view
                eventsView.style.display = 'block';

                console.log('✅ Events view shown');
            } else {
                console.error('❌ Events, friends, or chat view not found');
            }
        }
    </script>
}
