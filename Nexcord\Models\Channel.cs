using System.ComponentModel.DataAnnotations;

namespace Nexcord.Models
{
    public enum ChannelType
    {
        Text = 0,
        Voice = 1,
        Announcement = 2
    }

    public class Channel
    {
        public int Id { get; set; }

        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;

        [StringLength(500)]
        public string? Description { get; set; }

        public ChannelType Type { get; set; } = ChannelType.Text;

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public int Position { get; set; } = 0;

        [Required]
        public int ServerId { get; set; }

        // Navigation properties
        public virtual Server Server { get; set; } = null!;
        public virtual ICollection<Message> Messages { get; set; } = new List<Message>();
    }
}
