using System.ComponentModel.DataAnnotations;

namespace Nexcord.Models
{
    public enum FriendshipStatus
    {
        Pending = 0,
        Accepted = 1,
        Declined = 2,
        Blocked = 3
    }

    public class Friendship
    {
        public int Id { get; set; }

        [Required]
        public string RequesterId { get; set; } = string.Empty;

        [Required]
        public string AddresseeId { get; set; } = string.Empty;

        public FriendshipStatus Status { get; set; } = FriendshipStatus.Pending;

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime? AcceptedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }

        // Navigation properties
        public virtual ApplicationUser Requester { get; set; } = null!;
        public virtual ApplicationUser Addressee { get; set; } = null!;

        // Helper methods
        public bool IsPending => Status == FriendshipStatus.Pending;
        public bool IsAccepted => Status == FriendshipStatus.Accepted;
        public bool IsBlocked => Status == FriendshipStatus.Blocked;

        // Get the other user in the friendship
        public ApplicationUser GetOtherUser(string currentUserId)
        {
            return RequesterId == currentUserId ? Addressee : Requester;
        }

        // Check if current user is the requester
        public bool IsRequester(string currentUserId)
        {
            return RequesterId == currentUserId;
        }
    }
}
