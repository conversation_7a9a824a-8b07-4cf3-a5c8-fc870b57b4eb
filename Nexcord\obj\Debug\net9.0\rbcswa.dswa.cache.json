{"GlobalPropertiesHash": "2ilJ2M8+ZdH0swl4cXFj9Ji8kay0R08ISE/fEc+OL0o=", "FingerprintPatternsHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["aRAcYwNE0mwmP5QxlvyKoMoEbLy6Jb57F2sWHzMVSI8=", "pi6APhv6tK+HHG4z1jUf8wSNx7Bxi53mxlEL8Ino67U=", "dYKkHK8CScMLSzJbL02tLqGrpBzgeamrcW+PEPcKQqE=", "K2THbMzbSXqrlFBnBg/wYPa7j+up5TgFsgT3wR/Pz6Y=", "TAesjDSYqMAauvEJRIRTgvSh9mYqAt4NIuTY3zgYItk=", "I0+4iP2BZfqY4YXbmY5OQZe65tDtdl//d/PDlFDla9Y=", "sxVEiT4LMTPYzHky/+hbqGQ7m+qnGTEZ9y4WlCOAEwg=", "nk3ZJoZ8fHIXUWqnmQFt7ydJ90hAbQ0Y+/Bk/cMiD88=", "WDZ2+sM1kznJGkPpS6gaEPNiv6ZgJ0Ia65qeqRGUVSs=", "XF1LDFm5imML2kE+1hmgPw2jPmd0UdmdPl1ACMM1dcA=", "er972Z3jW2rbetTrfKUwBItYFaXbqzsMwwFos3xd9+U=", "Ssfwezt0zE936Te/UIZr3L2ZpfpWa2o4fQfQt65u4+M=", "yZhVK8twwaAXrArUAde0HoWkdLpk6XkyuAWIBiJX6fs=", "XEOrlzk5ihRaBig2loBb96q8cQqWvAT8WmMrMgp4GPs=", "Ty52q7/ldh884Fu278LDtaz/g4JywZ64sPue8uM5KMU=", "0Z7rEHJnJqE/RrY4YeEG/axarQjnuN2oJU90H5BeFhk=", "mwqawFKE1oIg23Km5ZwjEky4IZ+nLVsx6WWlEGWmsck=", "uyREzXWIdnexVJlv3tf/0lhHA9OxzwmqzirSDjqZShU=", "VIZ8UV6NaD+37kEUisvgbPSW1dh5qi7DtzD95UHCIng=", "hxNgDHZLOxvS0zOcyHuQDFLJsPk7KtkVQ9gokhr2z64=", "w7XLc4u1QJm5kp17lVCgqehT0u20igHJPfIQnDff/qA=", "o6CD6J1D/27+YUG59mPLZh1re2S2FFb3haZzTo6ACgs=", "nzCMttNibtpIRHViEa7OAMGjTMxfS+p2lP3X+yZc/Uc=", "f1nsRhCgNIBb7mRD6TQVu9qLA3xHwLrMjj9C3AQLUSE=", "7BlWw14Q8fxQI75vVC8vtGOHdxh/bWqzaliXt3oYCvU=", "jhoNq5AnTjkMjUvVXWANe4meCw9W8d4kW5LQ8S+/0QI=", "Qpuzzcp1rr5yk7FaHMGdY2acq0aze6ej4/i7R2yVlyQ=", "/WQTqOIdYIkfLrnJkRkEE3i2yf0G61rMy/u5hCljKuo=", "k4rWT1wOltxp0RKXAVtIcPmwnltRbcgo+/5cz6JZ6Qk=", "dtml5cowRDqX7OsgoTegpzUYFfuZoo2oLssgq9kivao=", "MqnhR6RPuWAixIqvZto75s/vejCego3hEVeT+TDTaak=", "HO6Xp13BSTHnGy4LLmF9rtyWRoQsFCTMgZF/irReBAg=", "D+RAIbmOE1ZtabNDr/4vHPwJimapHIrkpnLHyg9VkA8=", "u3DU8GpmmTn9rgyZvZeXUSyRHGttHQ17U6ZU5FToNhk=", "zWvGNtD6HOdMpoIBWaojSuO09ikxyvgX0gY6LeNeonY=", "V642YQye6XQYwjHz4+cGMxd3dpMtEsj9VRVcWfmDRjE=", "CYMa0IWr2xzuP7X74Rl6HMUXvmlRN1Ixo6e9wgdthAo=", "G/dJXQX/H4jbh2su3zKErhtpDijDW0L82+mL3I7OrFY=", "6HdotoPoDE7JjnPp5Rp7K/GkzNTWHXkaeGsvwxh9a7s=", "gyKixDfooaUSqkFgWRBH8gmNfF0FDxwbmgZqC9JQ36E=", "BzLGZGjNcZOPhMO2kH0RVxnBDf/wZLRE8OAhdcAByWk=", "3uvx3AMx7BBvJaOThfPbxpwARZ+BLY6fBg6yR2mF4cU=", "yPPjOXY7Cw+73+6rSt5awsQTag1MK2Iz6SYVgcs1vNU=", "8vKxZRv+lkBlmLU63oa2qfGOH7gFY9fgopc6mlKuLU8=", "julhKERR+92RxkeSvQnHis3VSNaysvujFLrCZqxb4pk=", "NGcxG+IGmOavbqo2h0Zi15ROqdtlkoSBiIgQ6NochEo=", "0qPjxaCpeQvOi+FWa0Tk711mjQ7GHOKaCtXydDo5Tx4=", "jgthRTmdwRZIlV0F1AoKToay+pN6+rDg4uspNAjITI0=", "mCG+5U/wvJdx2NQjjmxO0uQukouy9f+aIrpiCTp/D7k=", "eYvlaxUxU0ffErwUMltxNSAVhWn26rZtPqW8o2E1vU4=", "Gwq6CTmZZ0vEzGFUNcQ1T+cBQcPOI7rYAhsrg4MxMwA=", "y0A/bUc5P3NKcKPudXeuc4QA2/1QAtSEJvjBnaL2M1g=", "hs71nZ9JGheyinN1myOetYXGjP0pEBLQP8yLEVW1loA=", "hhxSH4Ch9x2NSk988DchxBoEDotsuMtO5zQUi5uRw0Y=", "j8Bp0baXI+6on2CsXqVuLGR6Xn38hnYYSY1GYWFJG5Q=", "d0t9pY022zEfke2qAeQLgriTVIzFYC8THv01ec4pZTg=", "OJu2+pi5CeX7HGV0jqWQVnI+aEqyjRyGUeQogk3+8Ws=", "geyew594t5fxrRO3s9EdSq2Sro9v70XK64Up4NQHvXM=", "wCUULXPXt9BdBUx1A+gt9UNBtoZx8eMFcySwUmPmVq0=", "3Dg/W3hvPPPlJJJTrrUn7PlsK76qjSyhYyJI9A5NgPY=", "zwUrfN/jTnZkKN/YAwJJAlRYCgEpUIuY5KNVq91fV78=", "WBeKEehSdcZQxwGFnXwfN1ETP/844Jj1uXMBhlu8zm0=", "aWk/IECL3aoXZjkR8rk/3rD1VNQi6nxoh0X9Z3xJj+c=", "pY/Smg9UT3qLT8BwPGUqSDxFL+eUZ1qdUGjQytVcUvw=", "2H/3Y7BQCDfCSuKMLyEXZLseSdNFI73aLsdNr250phc=", "xtqReSPzu9p0CF18KaH5cggnnYt7RLF9gXCsuES40iM=", "L0bYazI3iiQdWzCjNI5v4rAchWGiydTlillGK5LKQK4=", "kpss2CYm9JI++WJ+3sNxK75h5DVXKic/FCIaJaQzamo=", "Bbj0ISanIHNcWldHQTwNAISREMMoMmewGvej9HkjjHY=", "+SEOzN8vXtZyqAptju/txhei0oGASh+roOxq9DO+5W8=", "KOFoKZQPKFawJK7H731EjXhlH27XHyeWdmFrQJy8ACE=", "W5N7JOWno8seOYvJRd1f7iNBURawiev55ncYHQTqo6Q=", "TSyc6RIdVebEreBlUtJbcRsLnl7LoIwqXi9AJMTarfg=", "LFHthhC56UlLqb5aHtrIoZtrtGJUtXMLTT/nc4Wx+Fc=", "sK65lVAeBYm4VOVDaOhIOXje1qyAiHVAgMPC/N2FISU=", "bFEa2BzhgoozVtGBd796fYc2KKHyZvU+VI7MYwPETE0=", "7yRVGrrLWHCFiqZP79bZgQERzA0inOeZcTqOjCVmHTg=", "xhHFFmlEurQypW0mYq3OseeJaB8wKxerE5Mr3JO06no=", "cF9B55wazYCQKV7pURF9jE1PrS61MAjzT8z6EkEtvwU=", "2Th8DplLFF36RjL5YbgngYK7D7llnRgyPSvs5dSHZwc=", "vHi79db6pezZjE+h4Hl9zAQDf9H1+ALBa4ru/ZkIA+0=", "M5lOLvChcpc8ArPz7q67O8yV0JFiXK6FElOZzsvuxQQ=", "WocIZDs5w4en8ydsAYF61cmDpniDCbeS462ZK2x5GVA=", "pEGMtqd9Nk45cGpBx4fOw2zXmK2Qe0XBb8IlT9iqM0Y=", "S6cooXhTRoifHMm1MG78ZJ4x6UPgIzbs6YfypTCMRtk=", "DVEVype2dtTevV91tSfSiWZWRcUkzMFUWUKanmSE3WI=", "PtxMPjnrTkiJT5fZ2euW7uC13PC6ZOXqcUC/fkUhoDg=", "pPO3FmoFgKVeJCt/46XyaHeQJP2Q/ybl4Jscp2Z+KSw=", "WfYgfzgUGFj8GILerZwJ8DmSjR0LR/+VTtxmPPlLDfg=", "SnpinsuVah5IAWhDb155DuJ8BsG8xm13/RZPwDApqEc=", "tulSb56klb3+lSmSmN0dGoTFnHlIbjR+vAJUVNza3tU=", "F7wkhXfKY07J4MQQwu686Om5jLSv71yHd7BAtV9+SGk=", "k80WHpAGOzJ2GjyUJ0AkrRLVsy5ioXgeNqY+NIOADL8=", "eieUc9K0AmOcnMNCZtdcdqB1/31QVB3e7xkLnjqFTLI=", "8aCRy0P7FIFhUeDtuuWoaMGHLs/M2V1OgphJ15+pMD4=", "hWManhVMCR5iJEoOeg85bCepO6Fxy90KMfgSRHj8sxg=", "YEE1oqNsXt/pRIpvnSw9zFNEDlRufs5Sn6osxuQXfaQ=", "4y9uNC5IzTY8BhymjYT45IphGDj88imQMVRk9ll5b3g=", "c9yxnu4m/U0Q1nOfnXTS2Rz85Rk1YI7KNOFWv+V9oAc=", "1Qs9MDnt6WCnni4pEuxrckcRSCnuLDnW0C0ojWJvyP0=", "JAFu3gLsL7BqLMwbZAh8rvwaSTuXID7PppEP6S/oNuo=", "sixK7mY43ZAeMHissevHM0xjrB7yZ0VMnpicJ3xJsqI=", "JrpGFy3mr3G7lWxqz+lJFsDJpvVp4I2G5KzA68bgIi4=", "Hag/CnGFiAHIcc/bkM/VgYzoQ6+hkfl0aPGRhey8UaM=", "lQOff7ng7pYeXB1l4WtSnAzTxYA59tkHOTnhn0Xg7CU=", "B+jb3qfwcv/Gy88ooYErPdmtwxkxKs2shD1fyVWFMZQ=", "N1FYn5NLDISBRsnZY5Dkv8Q1QT4K1TTRvGWi5ql3d4o=", "h13GJqHLguxaZiiXG6AYbehncO8xUafVWU+jjfHMuSc=", "QGWwrz8qa61WO9zC7GSxmebZjWvOt1wrCHhEvIAzl7k=", "fBVUfL71n/mx4SN/aSnn3mGgNXdg+cBYnY7R1qy+WUY=", "9GwV+4MJ/k4ZCLk4hxBdNxTTSZZj5z923ZhW5DsXmJY=", "+l5PHTconDJZd/GubRz3maK1HD0zjsbyzA6NgNuHyu4=", "sKqVeaErZfg8PU4zoUN8EPWxVtb1BWAd02zSH8KJG4c=", "fNsDIOkipNzNWgoSb+xGewBUluxtKsniWyFQ8SUdfb4=", "CNGhWRCLVvneh2ARcOadWgB2p5TkoqjsJunAPZNPKbQ=", "6owaqgXKV3xn0PTen5179F0bKyK7Y958kWbwHJZSyJQ=", "8aCQzLu/ORvpQinNCHcFqFOXUysNmEqqGd2LV3FRzV8=", "yzMF90+PomjL01wwGMlQB4n/EkOqpztv2+c1+DMLGfw=", "+T0hRLOD17TtOzb9ZaAyJv4B7aNVnDQK4EJ5bjHFiJw=", "WToIUvnYrp2iQgQoVBreSoTvPmtTOfwPeI6yJphXiXo=", "B0MvlNVWT6N2AgSd495khpSZ4BlTElhpLLuLt5Uzxo4=", "Bk+ncUZXasn0unCByS+vsH+ArjyAIJa3Iu1lbDyKxi4=", "nLSeZdhnhMqjNorsin9zO3M7zbe8GnAfw8Lqyn8AhDM=", "6gdLZIj/0seXYbvD6TWdCvh+riT+oGMIeru/T2wh9xA=", "litfZ3UjNZWA7KsIiR1LCPQk6OAtFSTRcy4tCWSlt0w=", "4QntaL1ADQ4fq+qEdLQBwzttUM6807KF5NhRUKxUqhg="], "CachedAssets": {"4QntaL1ADQ4fq+qEdLQBwzttUM6807KF5NhRUKxUqhg=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\3hxgyqmuhp-re983pout9.gz", "SourceId": "Nexcord", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "Nexcord#[.{fingerprint=re983pout9}]!.bundle.scp.css.gz", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\Nexcord.bundle.scp.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mmk5acerye", "Integrity": "Um5eb0Ok2kv/4xTMR3sWzq4/38g0Mr72LN6ceT495fA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\Nexcord.bundle.scp.css", "FileLength": 539, "LastWriteTime": "2025-07-19T18:23:24.5653341+00:00"}, "litfZ3UjNZWA7KsIiR1LCPQk6OAtFSTRcy4tCWSlt0w=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\l37rrdn9dn-re983pout9.gz", "SourceId": "Nexcord", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "Nexcord#[.{fingerprint=re983pout9}]?.styles.css.gz", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\scopedcss\\bundle\\Nexcord.styles.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mmk5acerye", "Integrity": "Um5eb0Ok2kv/4xTMR3sWzq4/38g0Mr72LN6ceT495fA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\scopedcss\\bundle\\Nexcord.styles.css", "FileLength": 539, "LastWriteTime": "2025-07-19T18:23:24.5623305+00:00"}, "nLSeZdhnhMqjNorsin9zO3M7zbe8GnAfw8Lqyn8AhDM=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\n2ba4e8li9-mlv21k5csn.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/jquery/LICENSE#[.{fingerprint=mlv21k5csn}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\jquery\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k2cjzt041l", "Integrity": "JKWeHUaANNuffeRatdc54UCb84RoxjEw/nTq8Mops8Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\jquery\\LICENSE.txt", "FileLength": 682, "LastWriteTime": "2025-07-19T18:23:24.8135491+00:00"}, "Bk+ncUZXasn0unCByS+vsH+ArjyAIJa3Iu1lbDyKxi4=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\pqjggk2s14-87fc7y1x7t.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint=87fc7y1x7t}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p5ev1mo4sb", "Integrity": "bZLUSM9fRhNJKF4yFjKvv9iU/j2aC1lJtEqR9u5n6y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "FileLength": 43123, "LastWriteTime": "2025-07-19T18:23:25.3169109+00:00"}, "B0MvlNVWT6N2AgSd495khpSZ4BlTElhpLLuLt5Uzxo4=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\75o6zz68du-muycvpuwrr.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint=muycvpuwrr}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "trtvcfsjjx", "Integrity": "h68a4ojlMeHZ2MJNcpHGvBgERdoUw7Qpxk6a/oPD5kQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "FileLength": 24360, "LastWriteTime": "2025-07-19T18:23:25.1847596+00:00"}, "WToIUvnYrp2iQgQoVBreSoTvPmtTOfwPeI6yJphXiXo=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\6a5nda9alj-2z0ns9nrw6.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/jquery/dist/jquery.slim#[.{fingerprint=2z0ns9nrw6}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xlmiz42j7j", "Integrity": "9bWYPof03vxzc0YRmjwb6berDC7SXtE8rlvXxhIlakE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "FileLength": 68601, "LastWriteTime": "2025-07-19T18:23:25.0898977+00:00"}, "+T0hRLOD17TtOzb9ZaAyJv4B7aNVnDQK4EJ5bjHFiJw=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\qdytaxc5jf-ttgo8qnofa.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint=ttgo8qnofa}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hq2hiirxit", "Integrity": "GfL7M76amKU+B+H1o4WVh7q13GpiC7L6scp3ODOz3ag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 54456, "LastWriteTime": "2025-07-19T18:23:25.0161293+00:00"}, "yzMF90+PomjL01wwGMlQB4n/EkOqpztv2+c1+DMLGfw=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\fli5hie7gi-o1o13a6vjx.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint=o1o13a6vjx}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4efnowp72v", "Integrity": "rJUWNDEom3xIYh3mH5HCPpyh/CGhP0YhaSsaX6IOiDk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 30683, "LastWriteTime": "2025-07-19T18:23:24.977236+00:00"}, "8aCQzLu/ORvpQinNCHcFqFOXUysNmEqqGd2LV3FRzV8=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\n6n2xmictt-0i3buxo5is.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/jquery/dist/jquery#[.{fingerprint=0i3buxo5is}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\jquery\\dist\\jquery.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "slhp14mdwv", "Integrity": "YPYGuVoBNkA8pP/JjlyjSON9Q7Ej1gVEUkbHrQT3uJU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\jquery\\dist\\jquery.js", "FileLength": 84431, "LastWriteTime": "2025-07-19T18:23:24.9397354+00:00"}, "6owaqgXKV3xn0PTen5179F0bKyK7Y958kWbwHJZSyJQ=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\c29njwia6r-x0q3zqp4vz.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/jquery-validation/LICENSE#[.{fingerprint=x0q3zqp4vz}]?.md.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1fp9w2itex", "Integrity": "7bAe2yT3obcUnzDyFQyuh4GI4iC/NGB4Bs4gZaY3Wj4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "FileLength": 683, "LastWriteTime": "2025-07-19T18:23:24.9004615+00:00"}, "CNGhWRCLVvneh2ARcOadWgB2p5TkoqjsJunAPZNPKbQ=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\cjffkhr2pd-ag7o75518u.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min#[.{fingerprint=ag7o75518u}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hgmochx07c", "Integrity": "T/jNUyeLYdKQ3zndN8zs507jIg6GnkENMxuBqc1eXs0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 8121, "LastWriteTime": "2025-07-19T18:23:24.8974595+00:00"}, "fNsDIOkipNzNWgoSb+xGewBUluxtKsniWyFQ8SUdfb4=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\dznolve5q5-lzl9nlhx6b.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/jquery-validation/dist/jquery.validate#[.{fingerprint=lzl9nlhx6b}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "anlp8nykfw", "Integrity": "KuJPTvhuArDs4mj2zziNxM+hBWFbOhi0BKQgeR1pHG8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 14068, "LastWriteTime": "2025-07-19T18:23:24.8934596+00:00"}, "sKqVeaErZfg8PU4zoUN8EPWxVtb1BWAd02zSH8KJG4c=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\d532bwysf7-mrlpezrjn3.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/jquery-validation/dist/additional-methods.min#[.{fingerprint=mrlpezrjn3}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uya4g6am6w", "Integrity": "hhhjkgaWyJk2NWrF/LyhTIDOF42U+kBX72IU2K4RWJg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 6482, "LastWriteTime": "2025-07-19T18:23:24.8860889+00:00"}, "+l5PHTconDJZd/GubRz3maK1HD0zjsbyzA6NgNuHyu4=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\rnjlobj6hx-83jwlth58m.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/jquery-validation/dist/additional-methods#[.{fingerprint=83jwlth58m}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8j9rvpuvlg", "Integrity": "BIw6qCD4ysNHqPUgCkXJ/h+XlQ/h+2MuYEbvs/ZN+kM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 14078, "LastWriteTime": "2025-07-19T18:23:24.8821965+00:00"}, "9GwV+4MJ/k4ZCLk4hxBdNxTTSZZj5z923ZhW5DsXmJY=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\ddlidatjss-356vix0kms.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE#[.{fingerprint=356vix0kms}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yhmugv22u", "Integrity": "WpL0kbwDHONKwUu+fAC6nKQXtZOBY20Gsspcn336Iz4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 694, "LastWriteTime": "2025-07-19T18:23:24.8777071+00:00"}, "fBVUfL71n/mx4SN/aSnn3mGgNXdg+cBYnY7R1qy+WUY=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\zbulofmtvk-4v8eqarkd7.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min#[.{fingerprint=4v8eqarkd7}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kw5m9o3hck", "Integrity": "ZECRGyeVrglmY0fxP9wVujqjjSoecu3G7roYlvaINus=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "FileLength": 2207, "LastWriteTime": "2025-07-19T18:23:24.8747078+00:00"}, "QGWwrz8qa61WO9zC7GSxmebZjWvOt1wrCHhEvIAzl7k=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\c82kq708ap-47otxtyo56.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive#[.{fingerprint=47otxtyo56}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7mrxpa<PERSON><PERSON><PERSON>", "Integrity": "8WSd7G1SSJrTChp1H0sIwU3oPQXDsKXVF1KkXEF6LuI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "FileLength": 4651, "LastWriteTime": "2025-07-19T18:23:24.8717062+00:00"}, "h13GJqHLguxaZiiXG6AYbehncO8xUafVWU+jjfHMuSc=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\dmgigzlqef-0j3bgjxly4.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint=0j3bgjxly4}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n46fiwynw7", "Integrity": "OYFfBiMA8guQaokr7JUEKEquxVFRad17YsNbJdI0IKM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 55848, "LastWriteTime": "2025-07-19T18:23:24.8657033+00:00"}, "N1FYn5NLDISBRsnZY5Dkv8Q1QT4K1TTRvGWi5ql3d4o=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\34z6si6vvi-63fj8s7r0e.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint=63fj8s7r0e}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "on09t9pmch", "Integrity": "bIPgOT88ycSklHWxEzFQVPvNsgNbss3QQbyTqHho4PA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 16636, "LastWriteTime": "2025-07-19T18:23:24.8330283+00:00"}, "B+jb3qfwcv/Gy88ooYErPdmtwxkxKs2shD1fyVWFMZQ=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\wvdjqi8e6y-h1s4sie4z3.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint=h1s4sie4z3}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "42umladzh7", "Integrity": "rG54EGAHotdOswEyoMsIu5DDixozuuoKxcO7w4hcEQA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 64423, "LastWriteTime": "2025-07-19T18:23:24.8594841+00:00"}, "lQOff7ng7pYeXB1l4WtSnAzTxYA59tkHOTnhn0Xg7CU=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\cur2bgt2n0-notf2xhcfb.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint=notf2xhcfb}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4d5rpgxe6z", "Integrity": "6NzYRu+d/0puyGm6UFw/dwD9409WZbUx18xgnT/wQoQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 29569, "LastWriteTime": "2025-07-19T18:23:24.8442394+00:00"}, "Hag/CnGFiAHIcc/bkM/VgYzoQ6+hkfl0aPGRhey8UaM=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\dch5k5hzbb-y7v9cxd14o.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint=y7v9cxd14o}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "klzs96wner", "Integrity": "OOTujdl0QaxckSfKf4pISOdHdkWzUDKsaJmaS87CLzk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 56667, "LastWriteTime": "2025-07-19T18:23:24.8350283+00:00"}, "JrpGFy3mr3G7lWxqz+lJFsDJpvVp4I2G5KzA68bgIi4=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\8km6ea020z-jj8uyg4cgr.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint=jj8uyg4cgr}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "erkthljg5u", "Integrity": "WmNnoBgejwchZUYVA3o03QOAGOuMRS778DeXvhP6suo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 18635, "LastWriteTime": "2025-07-19T18:23:24.7352382+00:00"}, "sixK7mY43ZAeMHissevHM0xjrB7yZ0VMnpicJ3xJsqI=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\s897umqpwl-kbrnm935zg.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint=kbrnm935zg}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qmaqe8uvdz", "Integrity": "UkbVh5EDjQ9ElFF2VidPUKfIufgj0++IuUkiaieELZw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 64130, "LastWriteTime": "2025-07-19T18:23:24.7309186+00:00"}, "JAFu3gLsL7BqLMwbZAh8rvwaSTuXID7PppEP6S/oNuo=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\veyx1xsr92-vr1egmr9el.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint=vr1egmr9el}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jtj7j0yqni", "Integrity": "6QziFU3u5nXZAGW+7TwN4NhocqzFBknypP5iUK5YK8k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 28852, "LastWriteTime": "2025-07-19T18:23:24.7146265+00:00"}, "1Qs9MDnt6WCnni4pEuxrckcRSCnuLDnW0C0ojWJvyP0=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\urcblhkvo8-iovd86k7lj.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint=iovd86k7lj}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "svb68clhd0", "Integrity": "WDZTOK9dQrex7lgPEZZ+JZhLPdAF5GijB8N4Mslft/0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 86956, "LastWriteTime": "2025-07-19T18:23:24.7033157+00:00"}, "c9yxnu4m/U0Q1nOfnXTS2Rz85Rk1YI7KNOFWv+V9oAc=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\kqr05g8xvw-493y06b0oq.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint=493y06b0oq}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9t6fi9687k", "Integrity": "PUb7rj1jLHgIo7Hwm3lvukBcGKDry7n7W2fa1xrz+zY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 23984, "LastWriteTime": "2025-07-19T18:23:24.8307972+00:00"}, "4y9uNC5IzTY8BhymjYT45IphGDj88imQMVRk9ll5b3g=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\onqlacepr7-6pdc2jztkx.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint=6pdc2jztkx}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sgz3bfuucz", "Integrity": "tELcWYAIUkPDirIRIOTlom3Q4rdUDcA6PdYMCRE48xY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 92045, "LastWriteTime": "2025-07-19T18:23:24.8063504+00:00"}, "YEE1oqNsXt/pRIpvnSw9zFNEDlRufs5Sn6osxuQXfaQ=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\s2r2173bh3-6cfz1n2cew.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint=6cfz1n2cew}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sh9sbpd29q", "Integrity": "8KHWfFCoPlSmLyTbXOoHUNBddvrRpRlyAbs4j5nKGKY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 44354, "LastWriteTime": "2025-07-19T18:23:24.7176254+00:00"}, "hWManhVMCR5iJEoOeg85bCepO6Fxy90KMfgSRHj8sxg=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\gvmh3klurf-ft3s53vfgj.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint=ft3s53vfgj}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j9d5qn8h15", "Integrity": "mVddgYoZfee39UGvBjujrPfkX4g9o5fJQgtcRjDKhDc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 91702, "LastWriteTime": "2025-07-19T18:23:24.7033157+00:00"}, "8aCRy0P7FIFhUeDtuuWoaMGHLs/M2V1OgphJ15+pMD4=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\ttetacwn98-pk9g2wxc8p.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint=pk9g2wxc8p}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ih1ajc97pa", "Integrity": "Bhl6D0ngVAgx68NwXp2DEDO390PSrA5dlFHCQXY4WgM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 30986, "LastWriteTime": "2025-07-19T18:23:24.6109981+00:00"}, "eieUc9K0AmOcnMNCZtdcdqB1/31QVB3e7xkLnjqFTLI=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\oz4dm1cloq-hrwsygsryq.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint=hrwsygsryq}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hack831yxc", "Integrity": "xwBA3wRtW8i96gsexkmrLvL85Ad0ueCN6i7I23oFCMU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 114953, "LastWriteTime": "2025-07-19T18:23:24.5601469+00:00"}, "k80WHpAGOzJ2GjyUJ0AkrRLVsy5ioXgeNqY+NIOADL8=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\hfqe4n8vwx-37tfw0ft22.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint=37tfw0ft22}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qhts6dsckw", "Integrity": "Tl7d+IXzMoFjiGRivA39XpNjGWA6jMfITy87ywcah6c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 33101, "LastWriteTime": "2025-07-19T18:23:24.5349106+00:00"}, "F7wkhXfKY07J4MQQwu686Om5jLSv71yHd7BAtV9+SGk=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\d7p1td08qk-v0zj4ognzu.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint=v0zj4ognzu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "we7ylowkap", "Integrity": "+BDBQp6fX0jhehydJj3yEmXwPsq4ccmpRwJadVX8HUA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 91807, "LastWriteTime": "2025-07-19T18:23:24.7434468+00:00"}, "tulSb56klb3+lSmSmN0dGoTFnHlIbjR+vAJUVNza3tU=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\ir5ywmy3db-46ein0sx1k.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint=46ein0sx1k}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9buzyrzsnb", "Integrity": "NWIxwejcHtJ5yvljTypwFQBimL4GY/TpmkwWCoiPk+o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 30963, "LastWriteTime": "2025-07-19T18:23:24.8175503+00:00"}, "SnpinsuVah5IAWhDb155DuJ8BsG8xm13/RZPwDApqEc=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\8vpzws9nyy-pj5nd1wqec.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint=pj5nd1wqec}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1jikaxfu3u", "Integrity": "M4d5aODk+LnhCUggc/Xb6RX+Jh4E7X4KN58JXJR757I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 115009, "LastWriteTime": "2025-07-19T18:23:24.7890089+00:00"}, "WfYgfzgUGFj8GILerZwJ8DmSjR0LR/+VTtxmPPlLDfg=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\340s5hso2v-s35ty4nyc5.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint=s35ty4nyc5}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jw1qs72mo9", "Integrity": "I0QuKxdK89NxyamT6EeIfl/MyifdDw+D8cUjkiXwoOU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 33251, "LastWriteTime": "2025-07-19T18:23:24.7598562+00:00"}, "pPO3FmoFgKVeJCt/46XyaHeQJP2Q/ybl4Jscp2Z+KSw=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\qtkus9k3am-nvvlpmu67g.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint=nvvlpmu67g}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oaf6lwhfh1", "Integrity": "8BfOknNd4oMU2u3DUY0C/Sjhoh3NGtdqE8kxApMdM2w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 24293, "LastWriteTime": "2025-07-19T18:23:24.7444487+00:00"}, "PtxMPjnrTkiJT5fZ2euW7uC13PC6ZOXqcUC/fkUhoDg=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\qirrv7664x-06098lyss8.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint=06098lyss8}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1dromj56xs", "Integrity": "hXLxKxNQS6hkMWOc1Po5uxTK8ovzNg0xvRC9wMKOZiM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 11046, "LastWriteTime": "2025-07-19T18:23:24.6598575+00:00"}, "DVEVype2dtTevV91tSfSiWZWRcUkzMFUWUKanmSE3WI=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\53eb89ku88-j5mq2jizvt.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint=j5mq2jizvt}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nd4sjw69va", "Integrity": "KxjxxNhsaUlb9m0XwKNiMkNh6OuNbjdGXY0bmR5CTyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 44095, "LastWriteTime": "2025-07-19T18:23:24.6523877+00:00"}, "S6cooXhTRoifHMm1MG78ZJ4x6UPgIzbs6YfypTCMRtk=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\0ke1uhj14c-tdbxkamptv.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint=tdbxkamptv}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "c9nucnnawf", "Integrity": "QAnDcxiLhrclwEVeKtd/GREdZNbXO2rZP5agorcS5EM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 11933, "LastWriteTime": "2025-07-19T18:23:24.6457007+00:00"}, "pEGMtqd9Nk45cGpBx4fOw2zXmK2Qe0XBb8IlT9iqM0Y=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\w2ilq12tud-c2oey78nd0.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint=c2oey78nd0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "q71g5sacw1", "Integrity": "Jh/LtOdwAMNcT5vpbDEGsxe6Xv18LR1JqK4h/+hvs5g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 24341, "LastWriteTime": "2025-07-19T18:23:24.6401905+00:00"}, "WocIZDs5w4en8ydsAYF61cmDpniDCbeS462ZK2x5GVA=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\o3rh16dtbu-lcd1t2u6c8.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint=lcd1t2u6c8}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "iqwesyid6h", "Integrity": "xp5LPZ0vlqmxQrG+KjPm7ijhhJ+gD7VeH35lOUwBTWM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 11063, "LastWriteTime": "2025-07-19T18:23:24.6608569+00:00"}, "M5lOLvChcpc8ArPz7q67O8yV0JFiXK6FElOZzsvuxQQ=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\asdzb1i8vf-r4e9w2rdcm.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint=r4e9w2rdcm}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mns2a2zywm", "Integrity": "kj9zDkFgjDpUKwWasvv6a42LMVfqKjl/Ji5Z5LCNoRE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 44123, "LastWriteTime": "2025-07-19T18:23:24.6568605+00:00"}, "vHi79db6pezZjE+h4Hl9zAQDf9H1+ALBa4ru/ZkIA+0=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\xlgx6agu47-khv3u5hwcm.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint=khv3u5hwcm}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bub42mguo1", "Integrity": "8+5oxr92QYcYeV3zAk8RS4XmZo6Y5i3ZmbiJXj9KVQo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 11991, "LastWriteTime": "2025-07-19T18:23:24.6466988+00:00"}, "2Th8DplLFF36RjL5YbgngYK7D7llnRgyPSvs5dSHZwc=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\vq0omhigoi-jd9uben2k1.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint=jd9uben2k1}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "l17cqhtmgf", "Integrity": "V8PtmOQL2VKj+/TynmuZNdDDyU9qG1QJ7FFnxVcN6Y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 15054, "LastWriteTime": "2025-07-19T18:23:24.63919+00:00"}, "cF9B55wazYCQKV7pURF9jE1PrS61MAjzT8z6EkEtvwU=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\pql65up4y9-dxx9fxp4il.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint=dxx9fxp4il}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cv8bd5rjwi", "Integrity": "qNJnRAEMymFdtmi8gvc2VbVtDDk/UjeSnp+VQ10+cl0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 3246, "LastWriteTime": "2025-07-19T18:23:24.6318232+00:00"}, "xhHFFmlEurQypW0mYq3OseeJaB8wKxerE5Mr3JO06no=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\brbtw8cll6-ee0r1s7dh0.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint=ee0r1s7dh0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bxpwye2e51", "Integrity": "wbodRxtgYaqKj+oSPNLIcKGMPziM41to/lFFylYjPBc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 25833, "LastWriteTime": "2025-07-19T18:23:24.6278211+00:00"}, "7yRVGrrLWHCFiqZP79bZgQERzA0inOeZcTqOjCVmHTg=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\ve22os7n99-rzd6atqjts.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint=rzd6atqjts}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ukcr8jbv0r", "Integrity": "bhwmNaLC/7dOUfZxpXsnreiNsp2lbllRXrZLXndYFgA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 3367, "LastWriteTime": "2025-07-19T18:23:24.619357+00:00"}, "bFEa2BzhgoozVtGBd796fYc2KKHyZvU+VI7MYwPETE0=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\c1jcslm5o8-fsbi9cje9m.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint=fsbi9cje9m}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2wjbbjit2u", "Integrity": "crByaO07mpJWuBndE5BbjmmKh3fj/Y0m8CmJDe+c3UQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 12587, "LastWriteTime": "2025-07-19T18:23:24.609998+00:00"}, "sK65lVAeBYm4VOVDaOhIOXje1qyAiHVAgMPC/N2FISU=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\b1z6sdf1so-b7pk76d08c.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint=b7pk76d08c}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "r79n6nskqp", "Integrity": "Z9B/f6Ax/2JeBbNo3F1oaFvmOzRvs3yS0nnykt272Wc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 3213, "LastWriteTime": "2025-07-19T18:23:24.6321879+00:00"}, "LFHthhC56UlLqb5aHtrIoZtrtGJUtXMLTT/nc4Wx+Fc=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\r032wci4vz-fvhpjtyr6v.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint=fvhpjtyr6v}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yedk7y2ovv", "Integrity": "FjCFey27wknG6tewZOhPfnDgvIw+sTBly7wTSXKSd8M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 25821, "LastWriteTime": "2025-07-19T18:23:24.6288226+00:00"}, "TSyc6RIdVebEreBlUtJbcRsLnl7LoIwqXi9AJMTarfg=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\3ewamp8wlr-ub07r2b239.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint=ub07r2b239}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4tzbrq9c6f", "Integrity": "+EIaQ03ZHgfVopnrJFjz7ZgQSAO9GeMOK+bzccTIQyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 3380, "LastWriteTime": "2025-07-19T18:23:24.6223569+00:00"}, "W5N7JOWno8seOYvJRd1f7iNBURawiev55ncYHQTqo6Q=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\sgmmv3mbgk-cosvhxvwiu.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint=cosvhxvwiu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d7q4cn5biw", "Integrity": "V0pKRwbw4DysvYMPCNK3s+wSdDLvMWJFO+hKrptop7A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 13815, "LastWriteTime": "2025-07-19T18:23:24.6173583+00:00"}, "KOFoKZQPKFawJK7H731EjXhlH27XHyeWdmFrQJy8ACE=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\dgm9v586gb-k8d9w2qqmf.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint=k8d9w2qqmf}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yj3hsx47l", "Integrity": "ujhFfu7SIw4cL8FWI0ezmD29C7bGSesJvlEcySm5beY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 5971, "LastWriteTime": "2025-07-19T18:23:24.555147+00:00"}, "+SEOzN8vXtZyqAptju/txhei0oGASh+roOxq9DO+5W8=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\enm8bet7a9-ausgxo2sd3.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint=ausgxo2sd3}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vo1c50q1ou", "Integrity": "cWfRgogdfOCr54Ae/lxY515FP4TJyUwc4Ae6uejBPI0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 32793, "LastWriteTime": "2025-07-19T18:23:24.5486385+00:00"}, "Bbj0ISanIHNcWldHQTwNAISREMMoMmewGvej9HkjjHY=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\w7fw5zhm6c-d7shbmvgxk.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint=d7shbmvgxk}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "td9xh3ux7u", "Integrity": "P5V7Xl3ceCLw6wDeOyAezE6gOa9re3B7gTUN/H/cDsY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 6749, "LastWriteTime": "2025-07-19T18:23:24.5419092+00:00"}, "kpss2CYm9JI++WJ+3sNxK75h5DVXKic/FCIaJaQzamo=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\njkfy0jcs9-aexeepp0ev.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint=aexeepp0ev}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p2onhk81wj", "Integrity": "oRq8VZWOZX9mMbVVZBzw8rSxg8D8d6u0L0zM8MMIvaE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 13807, "LastWriteTime": "2025-07-19T18:23:24.5349106+00:00"}, "L0bYazI3iiQdWzCjNI5v4rAchWGiydTlillGK5LKQK4=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\89rxgglgqv-erw9l3u2r3.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint=erw9l3u2r3}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ff2i3b225l", "Integrity": "y2vSlIdcL+ImKFhcBMT5ujdAP1cyOZlHZK434Aiu0KA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 5969, "LastWriteTime": "2025-07-19T18:23:24.5285884+00:00"}, "xtqReSPzu9p0CF18KaH5cggnnYt7RLF9gXCsuES40iM=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\63crkw9lqd-c2jlpeoesf.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint=c2jlpeoesf}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "goh6yfn9uv", "Integrity": "ALPf6qsZMu+Ui8l8jPJJF3MhTcq6uwrQhRWeWJL4ixU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 32794, "LastWriteTime": "2025-07-19T18:23:24.5255913+00:00"}, "2H/3Y7BQCDfCSuKMLyEXZLseSdNFI73aLsdNr250phc=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\95cqc3g0fx-bqjiyaj88i.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint=bqjiyaj88i}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yf1pcrzlgs", "Integrity": "jhvPrvWZn8BbeR49W+r+MLNcnTeFyaSXxry9n1ctwy4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 6745, "LastWriteTime": "2025-07-19T18:23:24.5359081+00:00"}, "pY/Smg9UT3qLT8BwPGUqSDxFL+eUZ1qdUGjQytVcUvw=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\azrmud3x8b-xtxxf3hu2r.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "js/site#[.{fingerprint=xtxxf3hu2r}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\js\\site.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rl5dcbfpcw", "Integrity": "Ydq7YvQUzhbo68waLZeQvZnPOvmTeQ+HyDqjkA1whsA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\js\\site.js", "FileLength": 189, "LastWriteTime": "2025-07-19T18:23:24.5116901+00:00"}, "dYKkHK8CScMLSzJbL02tLqGrpBzgeamrcW+PEPcKQqE=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\vltxs1u3vm-xtxxf3hu2r.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "js/site.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\js\\site.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rl5dcbfpcw", "Integrity": "Ydq7YvQUzhbo68waLZeQvZnPOvmTeQ+HyDqjkA1whsA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\js\\site.js", "FileLength": 189, "LastWriteTime": "2025-07-19T18:23:24.5141446+00:00"}, "aWk/IECL3aoXZjkR8rk/3rD1VNQi6nxoh0X9Z3xJj+c=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\m1mt0no8v7-61n19gt1b8.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "favicon#[.{fingerprint=61n19gt1b8}]?.ico.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\favicon.ico", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nezsohjtde", "Integrity": "wfFuuYm+Lh6RbCeeiUqxw334b/hIOkp5j9eokGUM1Y0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\favicon.ico", "FileLength": 2468, "LastWriteTime": "2025-07-19T18:23:24.5094164+00:00"}, "6gdLZIj/0seXYbvD6TWdCvh+riT+oGMIeru/T2wh9xA=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\dj2881k1b2-qa529ixwsh.gz", "SourceId": "Nexcord", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Nexcord", "RelativePath": "css/site#[.{fingerprint=qa529ixwsh}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\css\\site.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "z6fpcojqgw", "Integrity": "K7VFXd38ppASSD1PH+v8oF8nBrm/N01kQ6de09qXfNc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\wwwroot\\css\\site.css", "FileLength": 8177, "LastWriteTime": "2025-07-19T19:13:03.0102516+00:00"}, "WBeKEehSdcZQxwGFnXwfN1ETP/844Jj1uXMBhlu8zm0=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\yfc7ypekbg-mlv21k5csn.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/LICENSE.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k2cjzt041l", "Integrity": "JKWeHUaANNuffeRatdc54UCb84RoxjEw/nTq8Mops8Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\LICENSE.txt", "FileLength": 682, "LastWriteTime": "2025-07-19T18:23:24.8777071+00:00"}, "zwUrfN/jTnZkKN/YAwJJAlRYCgEpUIuY5KNVq91fV78=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\v0zgfp0y55-87fc7y1x7t.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.slim.min.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p5ev1mo4sb", "Integrity": "bZLUSM9fRhNJKF4yFjKvv9iU/j2aC1lJtEqR9u5n6y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.min.map", "FileLength": 43123, "LastWriteTime": "2025-07-19T18:23:24.8747078+00:00"}, "3Dg/W3hvPPPlJJJTrrUn7PlsK76qjSyhYyJI9A5NgPY=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\nezrqnk58p-muycvpuwrr.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.slim.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "trtvcfsjjx", "Integrity": "h68a4ojlMeHZ2MJNcpHGvBgERdoUw7Qpxk6a/oPD5kQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.min.js", "FileLength": 24360, "LastWriteTime": "2025-07-19T18:23:24.863704+00:00"}, "wCUULXPXt9BdBUx1A+gt9UNBtoZx8eMFcySwUmPmVq0=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\a3ygkwa5zy-2z0ns9nrw6.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.slim.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xlmiz42j7j", "Integrity": "9bWYPof03vxzc0YRmjwb6berDC7SXtE8rlvXxhIlakE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.js", "FileLength": 68601, "LastWriteTime": "2025-07-19T18:23:24.8574865+00:00"}, "geyew594t5fxrRO3s9EdSq2Sro9v70XK64Up4NQHvXM=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\b9lhb08218-ttgo8qnofa.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.min.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hq2hiirxit", "Integrity": "GfL7M76amKU+B+H1o4WVh7q13GpiC7L6scp3ODOz3ag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 54456, "LastWriteTime": "2025-07-19T18:23:24.8083524+00:00"}, "OJu2+pi5CeX7HGV0jqWQVnI+aEqyjRyGUeQogk3+8Ws=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\t0qo7o574p-o1o13a6vjx.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4efnowp72v", "Integrity": "rJUWNDEom3xIYh3mH5HCPpyh/CGhP0YhaSsaX6IOiDk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 30683, "LastWriteTime": "2025-07-19T18:23:24.7900086+00:00"}, "d0t9pY022zEfke2qAeQLgriTVIzFYC8THv01ec4pZTg=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\tpsq5dibur-0i3buxo5is.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "slhp14mdwv", "Integrity": "YPYGuVoBNkA8pP/JjlyjSON9Q7Ej1gVEUkbHrQT3uJU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.js", "FileLength": 84431, "LastWriteTime": "2025-07-19T18:23:24.7816503+00:00"}, "j8Bp0baXI+6on2CsXqVuLGR6Xn38hnYYSY1GYWFJG5Q=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\9muusbdg0a-x0q3zqp4vz.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/LICENSE.md.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\LICENSE.md", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1fp9w2itex", "Integrity": "7bAe2yT3obcUnzDyFQyuh4GI4iC/NGB4Bs4gZaY3Wj4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\LICENSE.md", "FileLength": 683, "LastWriteTime": "2025-07-19T18:23:24.7568574+00:00"}, "hhxSH4Ch9x2NSk988DchxBoEDotsuMtO5zQUi5uRw0Y=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\2tikzqlmtu-ag7o75518u.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hgmochx07c", "Integrity": "T/jNUyeLYdKQ3zndN8zs507jIg6GnkENMxuBqc1eXs0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 8121, "LastWriteTime": "2025-07-19T18:23:24.7514458+00:00"}, "hs71nZ9JGheyinN1myOetYXGjP0pEBLQP8yLEVW1loA=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\uocis9wxbx-lzl9nlhx6b.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/dist/jquery.validate.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\jquery.validate.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "anlp8nykfw", "Integrity": "KuJPTvhuArDs4mj2zziNxM+hBWFbOhi0BKQgeR1pHG8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 14068, "LastWriteTime": "2025-07-19T18:23:24.7444487+00:00"}, "y0A/bUc5P3NKcKPudXeuc4QA2/1QAtSEJvjBnaL2M1g=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\t6e3b82hrf-mrlpezrjn3.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/dist/additional-methods.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\additional-methods.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uya4g6am6w", "Integrity": "hhhjkgaWyJk2NWrF/LyhTIDOF42U+kBX72IU2K4RWJg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 6482, "LastWriteTime": "2025-07-19T18:23:24.7322382+00:00"}, "Gwq6CTmZZ0vEzGFUNcQ1T+cBQcPOI7rYAhsrg4MxMwA=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\ypthv7q62w-83jwlth58m.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/dist/additional-methods.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\additional-methods.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8j9rvpuvlg", "Integrity": "BIw6qCD4ysNHqPUgCkXJ/h+XlQ/h+2MuYEbvs/ZN+kM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 14078, "LastWriteTime": "2025-07-19T18:23:24.7279175+00:00"}, "eYvlaxUxU0ffErwUMltxNSAVhWn26rZtPqW8o2E1vU4=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\ldxy2n3w6q-356vix0kms.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yhmugv22u", "Integrity": "WpL0kbwDHONKwUu+fAC6nKQXtZOBY20Gsspcn336Iz4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 694, "LastWriteTime": "2025-07-19T18:23:24.7216288+00:00"}, "mCG+5U/wvJdx2NQjjmxO0uQukouy9f+aIrpiCTp/D7k=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\q3naettu8c-4v8eqarkd7.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kw5m9o3hck", "Integrity": "ZECRGyeVrglmY0fxP9wVujqjjSoecu3G7roYlvaINus=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "FileLength": 2207, "LastWriteTime": "2025-07-19T18:23:24.7146265+00:00"}, "jgthRTmdwRZIlV0F1AoKToay+pN6+rDg4uspNAjITI0=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\j3hwsq15kh-47otxtyo56.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7mrxpa<PERSON><PERSON><PERSON>", "Integrity": "8WSd7G1SSJrTChp1H0sIwU3oPQXDsKXVF1KkXEF6LuI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "FileLength": 4651, "LastWriteTime": "2025-07-19T18:23:24.7033157+00:00"}, "0qPjxaCpeQvOi+FWa0Tk711mjQ7GHOKaCtXydDo5Tx4=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\pogzkicjpz-0j3bgjxly4.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n46fiwynw7", "Integrity": "OYFfBiMA8guQaokr7JUEKEquxVFRad17YsNbJdI0IKM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 55848, "LastWriteTime": "2025-07-19T18:23:24.6961605+00:00"}, "NGcxG+IGmOavbqo2h0Zi15ROqdtlkoSBiIgQ6NochEo=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\zap00c0tb5-63fj8s7r0e.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "on09t9pmch", "Integrity": "bIPgOT88ycSklHWxEzFQVPvNsgNbss3QQbyTqHho4PA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 16636, "LastWriteTime": "2025-07-19T18:23:24.6846828+00:00"}, "julhKERR+92RxkeSvQnHis3VSNaysvujFLrCZqxb4pk=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\igscs4x0yk-h1s4sie4z3.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "42umladzh7", "Integrity": "rG54EGAHotdOswEyoMsIu5DDixozuuoKxcO7w4hcEQA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 64423, "LastWriteTime": "2025-07-19T18:23:24.9407357+00:00"}, "8vKxZRv+lkBlmLU63oa2qfGOH7gFY9fgopc6mlKuLU8=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\zzna4nrm5w-notf2xhcfb.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4d5rpgxe6z", "Integrity": "6NzYRu+d/0puyGm6UFw/dwD9409WZbUx18xgnT/wQoQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 29569, "LastWriteTime": "2025-07-19T18:23:24.9128985+00:00"}, "yPPjOXY7Cw+73+6rSt5awsQTag1MK2Iz6SYVgcs1vNU=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\ym8tkpcl2i-y7v9cxd14o.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "klzs96wner", "Integrity": "OOTujdl0QaxckSfKf4pISOdHdkWzUDKsaJmaS87CLzk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 56667, "LastWriteTime": "2025-07-19T18:23:24.9004615+00:00"}, "3uvx3AMx7BBvJaOThfPbxpwARZ+BLY6fBg6yR2mF4cU=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\vw9sls9bhj-jj8uyg4cgr.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "erkthljg5u", "Integrity": "WmNnoBgejwchZUYVA3o03QOAGOuMRS778DeXvhP6suo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 18635, "LastWriteTime": "2025-07-19T18:23:24.8900905+00:00"}, "BzLGZGjNcZOPhMO2kH0RVxnBDf/wZLRE8OAhdcAByWk=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\s3tglgz8hr-kbrnm935zg.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qmaqe8uvdz", "Integrity": "UkbVh5EDjQ9ElFF2VidPUKfIufgj0++IuUkiaieELZw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 64130, "LastWriteTime": "2025-07-19T18:23:24.8103523+00:00"}, "gyKixDfooaUSqkFgWRBH8gmNfF0FDxwbmgZqC9JQ36E=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\higufxz8op-vr1egmr9el.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jtj7j0yqni", "Integrity": "6QziFU3u5nXZAGW+7TwN4NhocqzFBknypP5iUK5YK8k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 28852, "LastWriteTime": "2025-07-19T18:23:24.7557816+00:00"}, "6HdotoPoDE7JjnPp5Rp7K/GkzNTWHXkaeGsvwxh9a7s=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\ivy2s9gwh5-iovd86k7lj.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "svb68clhd0", "Integrity": "WDZTOK9dQrex7lgPEZZ+JZhLPdAF5GijB8N4Mslft/0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 86956, "LastWriteTime": "2025-07-19T18:23:24.7434468+00:00"}, "G/dJXQX/H4jbh2su3zKErhtpDijDW0L82+mL3I7OrFY=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\lr5hmrr0j7-493y06b0oq.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9t6fi9687k", "Integrity": "PUb7rj1jLHgIo7Hwm3lvukBcGKDry7n7W2fa1xrz+zY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 23984, "LastWriteTime": "2025-07-19T18:23:24.6401905+00:00"}, "CYMa0IWr2xzuP7X74Rl6HMUXvmlRN1Ixo6e9wgdthAo=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\xa379ftczi-6pdc2jztkx.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sgz3bfuucz", "Integrity": "tELcWYAIUkPDirIRIOTlom3Q4rdUDcA6PdYMCRE48xY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 92045, "LastWriteTime": "2025-07-19T18:23:24.7768397+00:00"}, "V642YQye6XQYwjHz4+cGMxd3dpMtEsj9VRVcWfmDRjE=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\0k1i85ntyh-6cfz1n2cew.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sh9sbpd29q", "Integrity": "8KHWfFCoPlSmLyTbXOoHUNBddvrRpRlyAbs4j5nKGKY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 44354, "LastWriteTime": "2025-07-19T18:23:24.7568574+00:00"}, "zWvGNtD6HOdMpoIBWaojSuO09ikxyvgX0gY6LeNeonY=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\qfr28sqcy1-ft3s53vfgj.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j9d5qn8h15", "Integrity": "mVddgYoZfee39UGvBjujrPfkX4g9o5fJQgtcRjDKhDc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 91702, "LastWriteTime": "2025-07-19T18:23:24.6784488+00:00"}, "u3DU8GpmmTn9rgyZvZeXUSyRHGttHQ17U6ZU5FToNhk=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\z408qb6q7a-pk9g2wxc8p.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ih1ajc97pa", "Integrity": "Bhl6D0ngVAgx68NwXp2DEDO390PSrA5dlFHCQXY4WgM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 30986, "LastWriteTime": "2025-07-19T18:23:24.590251+00:00"}, "D+RAIbmOE1ZtabNDr/4vHPwJimapHIrkpnLHyg9VkA8=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\rsc7qacj1u-hrwsygsryq.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hack831yxc", "Integrity": "xwBA3wRtW8i96gsexkmrLvL85Ad0ueCN6i7I23oFCMU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 114953, "LastWriteTime": "2025-07-19T18:23:24.5767757+00:00"}, "HO6Xp13BSTHnGy4LLmF9rtyWRoQsFCTMgZF/irReBAg=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\l186vlgaag-37tfw0ft22.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qhts6dsckw", "Integrity": "Tl7d+IXzMoFjiGRivA39XpNjGWA6jMfITy87ywcah6c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 33101, "LastWriteTime": "2025-07-19T18:23:24.5797774+00:00"}, "MqnhR6RPuWAixIqvZto75s/vejCego3hEVeT+TDTaak=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\bh4v3mdph9-v0zj4ognzu.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "we7ylowkap", "Integrity": "+BDBQp6fX0jhehydJj3yEmXwPsq4ccmpRwJadVX8HUA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 91807, "LastWriteTime": "2025-07-19T18:23:24.8462414+00:00"}, "dtml5cowRDqX7OsgoTegpzUYFfuZoo2oLssgq9kivao=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\4vzefxwp2m-46ein0sx1k.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9buzyrzsnb", "Integrity": "NWIxwejcHtJ5yvljTypwFQBimL4GY/TpmkwWCoiPk+o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 30963, "LastWriteTime": "2025-07-19T18:23:24.822795+00:00"}, "k4rWT1wOltxp0RKXAVtIcPmwnltRbcgo+/5cz6JZ6Qk=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\47012z8l2l-pj5nd1wqec.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1jikaxfu3u", "Integrity": "M4d5aODk+LnhCUggc/Xb6RX+Jh4E7X4KN58JXJR757I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 115009, "LastWriteTime": "2025-07-19T18:23:24.7951344+00:00"}, "/WQTqOIdYIkfLrnJkRkEE3i2yf0G61rMy/u5hCljKuo=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\ns4583i71s-s35ty4nyc5.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jw1qs72mo9", "Integrity": "I0QuKxdK89NxyamT6EeIfl/MyifdDw+D8cUjkiXwoOU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 33251, "LastWriteTime": "2025-07-19T18:23:24.7658578+00:00"}, "Qpuzzcp1rr5yk7FaHMGdY2acq0aze6ej4/i7R2yVlyQ=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\iznc766avz-nvvlpmu67g.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oaf6lwhfh1", "Integrity": "8BfOknNd4oMU2u3DUY0C/Sjhoh3NGtdqE8kxApMdM2w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 24293, "LastWriteTime": "2025-07-19T18:23:24.6784488+00:00"}, "jhoNq5AnTjkMjUvVXWANe4meCw9W8d4kW5LQ8S+/0QI=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\o5k23vqhrk-06098lyss8.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1dromj56xs", "Integrity": "hXLxKxNQS6hkMWOc1Po5uxTK8ovzNg0xvRC9wMKOZiM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 11046, "LastWriteTime": "2025-07-19T18:23:24.599973+00:00"}, "7BlWw14Q8fxQI75vVC8vtGOHdxh/bWqzaliXt3oYCvU=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\h9vwtoirpy-j5mq2jizvt.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nd4sjw69va", "Integrity": "KxjxxNhsaUlb9m0XwKNiMkNh6OuNbjdGXY0bmR5CTyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 44095, "LastWriteTime": "2025-07-19T18:23:24.592971+00:00"}, "f1nsRhCgNIBb7mRD6TQVu9qLA3xHwLrMjj9C3AQLUSE=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\7o8oyvng68-tdbxkamptv.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "c9nucnnawf", "Integrity": "QAnDcxiLhrclwEVeKtd/GREdZNbXO2rZP5agorcS5EM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 11933, "LastWriteTime": "2025-07-19T18:23:24.5807781+00:00"}, "nzCMttNibtpIRHViEa7OAMGjTMxfS+p2lP3X+yZc/Uc=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\cynp17w084-c2oey78nd0.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "q71g5sacw1", "Integrity": "Jh/LtOdwAMNcT5vpbDEGsxe6Xv18LR1JqK4h/+hvs5g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 24341, "LastWriteTime": "2025-07-19T18:23:24.5747761+00:00"}, "o6CD6J1D/27+YUG59mPLZh1re2S2FFb3haZzTo6ACgs=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\vsrbd71spn-lcd1t2u6c8.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "iqwesyid6h", "Integrity": "xp5LPZ0vlqmxQrG+KjPm7ijhhJ+gD7VeH35lOUwBTWM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 11063, "LastWriteTime": "2025-07-19T18:23:24.7299212+00:00"}, "w7XLc4u1QJm5kp17lVCgqehT0u20igHJPfIQnDff/qA=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\8xykfy6qmd-r4e9w2rdcm.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mns2a2zywm", "Integrity": "kj9zDkFgjDpUKwWasvv6a42LMVfqKjl/Ji5Z5LCNoRE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 44123, "LastWriteTime": "2025-07-19T18:23:24.7216288+00:00"}, "hxNgDHZLOxvS0zOcyHuQDFLJsPk7KtkVQ9gokhr2z64=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\1fc4q00scq-khv3u5hwcm.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bub42mguo1", "Integrity": "8+5oxr92QYcYeV3zAk8RS4XmZo6Y5i3ZmbiJXj9KVQo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 11991, "LastWriteTime": "2025-07-19T18:23:24.7033157+00:00"}, "VIZ8UV6NaD+37kEUisvgbPSW1dh5qi7DtzD95UHCIng=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\nlrl0f3xs3-jd9uben2k1.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "l17cqhtmgf", "Integrity": "V8PtmOQL2VKj+/TynmuZNdDDyU9qG1QJ7FFnxVcN6Y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 15054, "LastWriteTime": "2025-07-19T18:23:24.6981613+00:00"}, "uyREzXWIdnexVJlv3tf/0lhHA9OxzwmqzirSDjqZShU=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\tg53r17ghy-dxx9fxp4il.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cv8bd5rjwi", "Integrity": "qNJnRAEMymFdtmi8gvc2VbVtDDk/UjeSnp+VQ10+cl0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 3246, "LastWriteTime": "2025-07-19T18:23:24.6921597+00:00"}, "mwqawFKE1oIg23Km5ZwjEky4IZ+nLVsx6WWlEGWmsck=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\u8428c4e9i-ee0r1s7dh0.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bxpwye2e51", "Integrity": "wbodRxtgYaqKj+oSPNLIcKGMPziM41to/lFFylYjPBc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 25833, "LastWriteTime": "2025-07-19T18:23:24.6856834+00:00"}, "0Z7rEHJnJqE/RrY4YeEG/axarQjnuN2oJU90H5BeFhk=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\d5z9sfpxbi-rzd6atqjts.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ukcr8jbv0r", "Integrity": "bhwmNaLC/7dOUfZxpXsnreiNsp2lbllRXrZLXndYFgA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 3367, "LastWriteTime": "2025-07-19T18:23:24.5623305+00:00"}, "Ty52q7/ldh884Fu278LDtaz/g4JywZ64sPue8uM5KMU=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\8axix7wldr-fsbi9cje9m.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2wjbbjit2u", "Integrity": "crByaO07mpJWuBndE5BbjmmKh3fj/Y0m8CmJDe+c3UQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 12587, "LastWriteTime": "2025-07-19T18:23:24.559146+00:00"}, "XEOrlzk5ihRaBig2loBb96q8cQqWvAT8WmMrMgp4GPs=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\k8b25g0zwc-b7pk76d08c.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "r79n6nskqp", "Integrity": "Z9B/f6Ax/2JeBbNo3F1oaFvmOzRvs3yS0nnykt272Wc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 3213, "LastWriteTime": "2025-07-19T18:23:24.5506374+00:00"}, "yZhVK8twwaAXrArUAde0HoWkdLpk6XkyuAWIBiJX6fs=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\oxk5o6czdw-fvhpjtyr6v.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yedk7y2ovv", "Integrity": "FjCFey27wknG6tewZOhPfnDgvIw+sTBly7wTSXKSd8M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 25821, "LastWriteTime": "2025-07-19T18:23:24.5466437+00:00"}, "Ssfwezt0zE936Te/UIZr3L2ZpfpWa2o4fQfQt65u4+M=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\3tzsqhslk9-ub07r2b239.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4tzbrq9c6f", "Integrity": "+EIaQ03ZHgfVopnrJFjz7ZgQSAO9GeMOK+bzccTIQyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 3380, "LastWriteTime": "2025-07-19T18:23:24.5389091+00:00"}, "er972Z3jW2rbetTrfKUwBItYFaXbqzsMwwFos3xd9+U=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\sf8kf2lula-cosvhxvwiu.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d7q4cn5biw", "Integrity": "V0pKRwbw4DysvYMPCNK3s+wSdDLvMWJFO+hKrptop7A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 13815, "LastWriteTime": "2025-07-19T18:23:24.5335898+00:00"}, "XF1LDFm5imML2kE+1hmgPw2jPmd0UdmdPl1ACMM1dcA=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\edcrak57d7-k8d9w2qqmf.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yj3hsx47l", "Integrity": "ujhFfu7SIw4cL8FWI0ezmD29C7bGSesJvlEcySm5beY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 5971, "LastWriteTime": "2025-07-19T18:23:24.6321879+00:00"}, "WDZ2+sM1kznJGkPpS6gaEPNiv6ZgJ0Ia65qeqRGUVSs=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\bww8ud00yd-ausgxo2sd3.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vo1c50q1ou", "Integrity": "cWfRgogdfOCr54Ae/lxY515FP4TJyUwc4Ae6uejBPI0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 32793, "LastWriteTime": "2025-07-19T18:23:24.6268229+00:00"}, "nk3ZJoZ8fHIXUWqnmQFt7ydJ90hAbQ0Y+/Bk/cMiD88=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\hh8ihyobdx-d7shbmvgxk.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "td9xh3ux7u", "Integrity": "P5V7Xl3ceCLw6wDeOyAezE6gOa9re3B7gTUN/H/cDsY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 6749, "LastWriteTime": "2025-07-19T18:23:24.6109981+00:00"}, "sxVEiT4LMTPYzHky/+hbqGQ7m+qnGTEZ9y4WlCOAEwg=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\9glsckmvxo-aexeepp0ev.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p2onhk81wj", "Integrity": "oRq8VZWOZX9mMbVVZBzw8rSxg8D8d6u0L0zM8MMIvaE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 13807, "LastWriteTime": "2025-07-19T18:23:24.6069966+00:00"}, "I0+4iP2BZfqY4YXbmY5OQZe65tDtdl//d/PDlFDla9Y=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\eb27mqwgz2-erw9l3u2r3.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ff2i3b225l", "Integrity": "y2vSlIdcL+ImKFhcBMT5ujdAP1cyOZlHZK434Aiu0KA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 5969, "LastWriteTime": "2025-07-19T18:23:24.5275887+00:00"}, "TAesjDSYqMAauvEJRIRTgvSh9mYqAt4NIuTY3zgYItk=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\rh65p086id-c2jlpeoesf.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "goh6yfn9uv", "Integrity": "ALPf6qsZMu+Ui8l8jPJJF3MhTcq6uwrQhRWeWJL4ixU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 32794, "LastWriteTime": "2025-07-19T18:23:24.5191424+00:00"}, "K2THbMzbSXqrlFBnBg/wYPa7j+up5TgFsgT3wR/Pz6Y=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\14ipv7q33s-bqjiyaj88i.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yf1pcrzlgs", "Integrity": "jhvPrvWZn8BbeR49W+r+MLNcnTeFyaSXxry9n1ctwy4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 6745, "LastWriteTime": "2025-07-19T18:23:24.5231446+00:00"}, "pi6APhv6tK+HHG4z1jUf8wSNx7Bxi53mxlEL8Ino67U=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\j9swiz3yzq-90yqlj465b.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "favicon.ico.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\favicon.ico", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fxo9vb9gbf", "Integrity": "cwHpDnBTabxDzOHXFPOawb7ZlL3eysr4s1HfAD/K3vA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\favicon.ico", "FileLength": 9541, "LastWriteTime": "2025-07-19T18:23:24.5054141+00:00"}, "aRAcYwNE0mwmP5QxlvyKoMoEbLy6Jb57F2sWHzMVSI8=": {"Identity": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\ofa40bqe71-b9sayid5wm.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\nexcord\\Nexcord\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "css/site.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\css\\site.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0suozzrugb", "Integrity": "0FC1RbVkLkwKhJ1oi+r2Hw4Tm9av6DyJeo95YVvfA3Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.7\\staticwebassets\\V5\\css\\site.css", "FileLength": 318, "LastWriteTime": "2025-07-19T18:23:24.5074163+00:00"}}, "CachedCopyCandidates": {}}