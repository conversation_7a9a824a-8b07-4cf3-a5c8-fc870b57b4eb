/* Nexcord Gaming Theme - Purple & Black */
:root {
    --nexcord-primary: #7C3AED;
    --nexcord-primary-dark: #5B21B6;
    --nexcord-secondary: #EC4899;
    --nexcord-dark: #0F0F0F;
    --nexcord-dark-light: #1A1A1A;
    --nexcord-dark-lighter: #2D2D2D;
    --nexcord-text: #FFFFFF;
    --nexcord-text-muted: #A1A1AA;
    --nexcord-accent: #06B6D4;
    --nexcord-success: #10B981;
    --nexcord-warning: #F59E0B;
    --nexcord-danger: #EF4444;
}

* {
    box-sizing: border-box;
}

html {
    font-size: 14px;
    scroll-behavior: smooth;
}

@media (min-width: 768px) {
    html {
        font-size: 16px;
    }
}

body.nexcord-body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif;
    background: linear-gradient(135deg, var(--nexcord-dark) 0%, var(--nexcord-dark-light) 100%);
    color: var(--nexcord-text);
    margin: 0;
    padding: 0;
    min-height: 100vh;
    overflow-x: hidden;
}

/* Animated Background */
body.nexcord-body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 80%, rgba(124, 58, 237, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(236, 72, 153, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(6, 182, 212, 0.05) 0%, transparent 50%);
    z-index: -1;
    animation: backgroundPulse 10s ease-in-out infinite;
}

@keyframes backgroundPulse {
    0%, 100% { opacity: 0.5; }
    50% { opacity: 0.8; }
}

/* Navbar Styles */
.nexcord-navbar {
    background: rgba(15, 15, 15, 0.98);
    backdrop-filter: blur(15px);
    border-bottom: 1px solid rgba(124, 58, 237, 0.3);
    padding: 0.75rem 0;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    transition: all 0.3s ease;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.3);
}

.nexcord-navbar .container-fluid {
    padding: 0 1.5rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
}

@media (max-width: 575.98px) {
    .nexcord-navbar .container-fluid {
        padding: 0 1rem;
    }
}

.nexcord-brand {
    font-size: 2rem;
    font-weight: 800;
    color: var(--nexcord-primary);
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    transition: all 0.3s ease;
    letter-spacing: -0.5px;
}

.nexcord-brand:hover {
    color: var(--nexcord-secondary);
    transform: scale(1.02);
    text-decoration: none;
}

.nexcord-brand i {
    font-size: 2.2rem;
    animation: gamepadPulse 2s ease-in-out infinite;
}

@keyframes gamepadPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

.nexcord-nav-link {
    color: var(--nexcord-text-muted);
    text-decoration: none;
    padding: 0.75rem 1.25rem;
    border-radius: 12px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    margin: 0 0.25rem;
    position: relative;
    font-size: 0.95rem;
}

.nexcord-nav-link:hover {
    color: var(--nexcord-primary);
    background: rgba(124, 58, 237, 0.15);
    transform: translateY(-1px);
    text-decoration: none;
}

.nexcord-nav-link.active {
    color: var(--nexcord-primary);
    background: rgba(124, 58, 237, 0.2);
}

.nexcord-btn-primary {
    background: linear-gradient(135deg, var(--nexcord-primary), var(--nexcord-secondary));
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(124, 58, 237, 0.3);
}

.nexcord-btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(124, 58, 237, 0.4);
    color: white;
}

/* Dropdown Styles */
.nexcord-dropdown {
    background: var(--nexcord-dark-light);
    border: 1px solid rgba(124, 58, 237, 0.2);
    border-radius: 0.75rem;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
}

.nexcord-dropdown .dropdown-item {
    color: var(--nexcord-text-muted);
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
    border: none;
    background: transparent;
    width: 100%;
    text-align: left;
}

.nexcord-dropdown .dropdown-item:hover {
    background: rgba(124, 58, 237, 0.1);
    color: var(--nexcord-text);
}

/* Navbar Toggler Styles */
.navbar-toggler {
    border: none;
    padding: 0.5rem;
    background: rgba(124, 58, 237, 0.1);
    border-radius: 0.5rem;
    transition: all 0.3s ease;
}

.navbar-toggler:focus {
    box-shadow: 0 0 0 0.2rem rgba(124, 58, 237, 0.25);
}

.navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.8%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
    width: 1.5em;
    height: 1.5em;
}

.navbar-toggler:hover {
    background: rgba(124, 58, 237, 0.2);
}

/* Navbar Collapse Styles */
.navbar-collapse {
    background: rgba(15, 15, 15, 0.95);
    border-radius: 0.75rem;
    margin-top: 1rem;
    padding: 1rem;
    border: 1px solid rgba(124, 58, 237, 0.2);
}

@media (min-width: 576px) {
    .navbar-collapse {
        background: transparent;
        border: none;
        margin-top: 0;
        padding: 0;
    }
}

/* Navbar Navigation Styles */
.navbar-nav {
    align-items: center;
}

.navbar-nav .nav-item {
    margin: 0.25rem 0;
}

@media (min-width: 576px) {
    .navbar-nav .nav-item {
        margin: 0;
    }
}

/* Dropdown Menu Positioning */
.dropdown-menu {
    margin-top: 0.5rem;
}

/* Active Navigation Link */
.navbar-nav .nav-link.active,
.navbar-nav .nexcord-nav-link.active {
    color: var(--nexcord-primary) !important;
    background: rgba(124, 58, 237, 0.2);
}

/* Main Content */
.nexcord-main {
    min-height: calc(100vh - 200px);
    padding: 0;
}

.nexcord-main.with-header {
    padding-top: 80px; /* Account for fixed navbar */
}

.nexcord-main.no-header {
    padding-top: 0;
    min-height: 100vh;
}



.footer-link:hover {
    color: var(--nexcord-primary);
}

/* Hero Section */
.hero-section {
    position: relative;
    min-height: 100vh;
    display: flex;
    align-items: center;
    overflow: hidden;
}

.hero-content {
    position: relative;
    z-index: 2;
}

.hero-title {
    font-size: 4rem;
    font-weight: 800;
    line-height: 1.1;
    margin-bottom: 1.5rem;
}

.gradient-text {
    background: linear-gradient(135deg, var(--nexcord-primary), var(--nexcord-secondary));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradientShift 3s ease-in-out infinite;
}

@keyframes gradientShift {
    0%, 100% { filter: hue-rotate(0deg); }
    50% { filter: hue-rotate(30deg); }
}

.hero-subtitle {
    color: var(--nexcord-text-muted);
    font-weight: 400;
    font-size: 2rem;
}

.hero-description {
    font-size: 1.25rem;
    color: var(--nexcord-text-muted);
    margin-bottom: 2rem;
    line-height: 1.6;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    margin-bottom: 3rem;
    flex-wrap: wrap;
}

.btn-hero {
    padding: 1rem 2rem;
    border-radius: 0.75rem;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    font-size: 1.1rem;
}

.btn-hero.btn-primary {
    background: linear-gradient(135deg, var(--nexcord-primary), var(--nexcord-secondary));
    color: white;
    box-shadow: 0 8px 25px rgba(124, 58, 237, 0.3);
}

.btn-hero.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(124, 58, 237, 0.4);
    color: white;
}

.btn-hero.btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: var(--nexcord-text);
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
}

.btn-hero.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-3px);
    color: var(--nexcord-text);
}

.hero-stats {
    display: flex;
    gap: 2rem;
    flex-wrap: wrap;
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 2rem;
    font-weight: 700;
    color: var(--nexcord-primary);
}

.stat-label {
    display: block;
    color: var(--nexcord-text-muted);
    font-size: 0.9rem;
}

/* Hero Visual */
.hero-visual {
    position: relative;
    height: 500px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.floating-card {
    position: absolute;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 1rem;
    padding: 1rem 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--nexcord-text);
    font-weight: 600;
    animation: float 6s ease-in-out infinite;
}

.floating-card i {
    font-size: 1.5rem;
    color: var(--nexcord-primary);
}

.card-1 {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.card-2 {
    top: 60%;
    right: 20%;
    animation-delay: 2s;
}

.card-3 {
    bottom: 20%;
    left: 20%;
    animation-delay: 4s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

.hero-image {
    display: flex;
    align-items: center;
    justify-content: center;
}

.gaming-controller {
    font-size: 8rem;
    color: var(--nexcord-primary);
    animation: controllerPulse 4s ease-in-out infinite;
}

@keyframes controllerPulse {
    0%, 100% { transform: scale(1) rotate(0deg); }
    25% { transform: scale(1.1) rotate(5deg); }
    75% { transform: scale(1.1) rotate(-5deg); }
}

/* Hero Particles */
.hero-particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: 1;
}

.particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: var(--nexcord-primary);
    border-radius: 50%;
    animation: particleFloat 15s linear infinite;
}

.particle:nth-child(1) { left: 10%; animation-delay: 0s; }
.particle:nth-child(2) { left: 30%; animation-delay: 3s; }
.particle:nth-child(3) { left: 50%; animation-delay: 6s; }
.particle:nth-child(4) { left: 70%; animation-delay: 9s; }
.particle:nth-child(5) { left: 90%; animation-delay: 12s; }

@keyframes particleFloat {
    0% {
        transform: translateY(100vh) scale(0);
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        transform: translateY(-100px) scale(1);
        opacity: 0;
    }
}

/* Gaming Communities Section */
.gaming-communities {
    padding: 5rem 0;
    background: rgba(15, 15, 15, 0.5);
}

.section-header {
    margin-bottom: 4rem;
}

.section-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    background: linear-gradient(135deg, var(--nexcord-primary), var(--nexcord-secondary));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.section-subtitle {
    font-size: 1.25rem;
    color: var(--nexcord-text-muted);
}

.game-card {
    position: relative;
    background: linear-gradient(135deg, var(--nexcord-dark-light), var(--nexcord-dark-lighter));
    border-radius: 1.5rem;
    padding: 2rem;
    height: 300px;
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.1);
    cursor: pointer;
}

.game-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.game-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, transparent, rgba(255, 255, 255, 0.05));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.game-card:hover::before {
    opacity: 1;
}

.game-icon {
    font-size: 4rem;
    margin-bottom: 1.5rem;
    transition: all 0.3s ease;
}

.cs2-card .game-icon { color: #FF6B35; }
.dota2-card .game-icon { color: #D32F2F; }
.gta-card .game-icon { color: #4CAF50; }

.game-card:hover .game-icon {
    transform: scale(1.1) rotate(5deg);
}

.game-info h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--nexcord-text);
}

.game-info p {
    color: var(--nexcord-text-muted);
    margin-bottom: 1.5rem;
}

.game-stats {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.game-stats span {
    color: var(--nexcord-text-muted);
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.game-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent);
    padding: 2rem;
    transform: translateY(100%);
    transition: transform 0.3s ease;
}

.game-card:hover .game-overlay {
    transform: translateY(0);
}

.btn-join {
    background: linear-gradient(135deg, var(--nexcord-primary), var(--nexcord-secondary));
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    text-decoration: none;
    font-weight: 600;
    display: inline-block;
    transition: all 0.3s ease;
}

.btn-join:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(124, 58, 237, 0.4);
    color: white;
}

/* Authentication Styles */
.auth-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    padding: 2rem 0;
    background:
        radial-gradient(circle at 20% 80%, rgba(124, 58, 237, 0.15) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(236, 72, 153, 0.15) 0%, transparent 50%);
}

.auth-card {
    background: rgba(26, 26, 26, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 2rem;
    padding: 3rem;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    position: relative;
    overflow: hidden;
}

.auth-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--nexcord-primary), var(--nexcord-secondary), var(--nexcord-accent));
}

.auth-header {
    text-align: center;
    margin-bottom: 2rem;
}

.auth-logo {
    font-size: 4rem;
    color: var(--nexcord-primary);
    margin-bottom: 1rem;
    animation: logoFloat 3s ease-in-out infinite;
}

@keyframes logoFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.auth-title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--nexcord-text);
    margin-bottom: 0.5rem;
}

.auth-subtitle {
    color: var(--nexcord-text-muted);
    font-size: 1.1rem;
}

.auth-form {
    margin-bottom: 2rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    color: var(--nexcord-text);
    font-weight: 600;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.form-label i {
    color: var(--nexcord-primary);
}

.auth-input {
    width: 100%;
    padding: 1rem 1.25rem;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 0.75rem;
    color: var(--nexcord-text);
    font-size: 1rem;
    transition: all 0.3s ease;
}

.auth-input:focus {
    outline: none;
    border-color: var(--nexcord-primary);
    box-shadow: 0 0 0 3px rgba(124, 58, 237, 0.1);
    background: rgba(255, 255, 255, 0.08);
}

.auth-input::placeholder {
    color: var(--nexcord-text-muted);
}

.form-check {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.form-check-input {
    width: 1.25rem;
    height: 1.25rem;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 0.25rem;
}

.form-check-input:checked {
    background: var(--nexcord-primary);
    border-color: var(--nexcord-primary);
}

.form-check-label {
    color: var(--nexcord-text-muted);
    margin-bottom: 0;
}

.btn-auth {
    width: 100%;
    padding: 1rem 2rem;
    border: none;
    border-radius: 0.75rem;
    font-weight: 600;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    cursor: pointer;
}

.btn-auth.btn-primary {
    background: linear-gradient(135deg, var(--nexcord-primary), var(--nexcord-secondary));
    color: white;
    box-shadow: 0 8px 25px rgba(124, 58, 237, 0.3);
}

.btn-auth.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 35px rgba(124, 58, 237, 0.4);
}

.auth-footer {
    text-align: center;
    margin-bottom: 2rem;
}

.auth-footer p {
    color: var(--nexcord-text-muted);
    margin-bottom: 0.5rem;
}

.auth-link {
    color: var(--nexcord-primary);
    text-decoration: none;
    font-weight: 600;
    transition: color 0.3s ease;
}

.auth-link:hover {
    color: var(--nexcord-secondary);
}

.auth-divider {
    text-align: center;
    margin: 2rem 0;
    position: relative;
}

.auth-divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: rgba(255, 255, 255, 0.1);
}

.auth-divider span {
    background: rgba(26, 26, 26, 0.95);
    color: var(--nexcord-text-muted);
    padding: 0 1rem;
    position: relative;
    z-index: 1;
}

.social-login {
    display: flex;
    gap: 1rem;
}

.btn-social {
    flex: 1;
    padding: 0.75rem 1rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 0.5rem;
    background: rgba(255, 255, 255, 0.05);
    color: var(--nexcord-text);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    cursor: pointer;
    font-weight: 600;
}

.btn-social:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

.btn-discord:hover {
    border-color: #5865F2;
    color: #5865F2;
}

.btn-steam:hover {
    border-color: #1B2838;
    color: #66C0F4;
}

/* Discord-like Dashboard Layout */
.discord-layout {
    display: flex;
    height: calc(100vh - 80px);
    background: var(--nexcord-dark);
}

/* Server List (Leftmost sidebar) */
.server-list {
    width: 72px;
    background: var(--nexcord-dark);
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 12px 0;
    gap: 8px;
    overflow-y: auto;
}

.server-item {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: var(--nexcord-dark-light);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    color: var(--nexcord-text-muted);
    font-weight: 600;
    font-size: 18px;
}

.server-item:hover {
    border-radius: 16px;
    background: var(--nexcord-primary);
    color: white;
}

.server-item.nexcord-logo {
    background: var(--nexcord-primary);
    color: white;
    border-radius: 16px;
    font-size: 20px;
    margin-bottom: 8px;
    text-decoration: none;
}

.server-item.nexcord-logo:hover {
    background: var(--nexcord-primary-dark);
    transform: scale(1.05);
    color: white;
    text-decoration: none;
}

.server-item.active {
    border-radius: 16px;
    background: var(--nexcord-primary);
    color: white;
}

.server-item.active::before {
    content: '';
    position: absolute;
    left: -16px;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 40px;
    background: white;
    border-radius: 0 4px 4px 0;
}

.server-item img {
    width: 100%;
    height: 100%;
    border-radius: inherit;
    object-fit: cover;
}

.server-divider {
    width: 32px;
    height: 2px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 1px;
    margin: 4px 0;
}

.add-server {
    background: var(--nexcord-dark-light);
    border: 2px dashed rgba(255, 255, 255, 0.2);
    color: var(--nexcord-success);
}

.add-server:hover {
    background: var(--nexcord-success);
    color: white;
    border-color: var(--nexcord-success);
}

/* Channel List (Second sidebar) */
.channel-list {
    width: 240px;
    background: var(--nexcord-dark-light);
    display: flex;
    flex-direction: column;
}

.server-header {
    padding: 16px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
    transition: background 0.2s ease;
}

.server-header:hover {
    background: rgba(255, 255, 255, 0.05);
}

.server-header h3 {
    color: var(--nexcord-text);
    font-size: 16px;
    font-weight: 600;
    margin: 0;
}

.server-header i {
    color: var(--nexcord-text-muted);
    font-size: 12px;
}

.channel-section {
    padding: 16px 8px 0;
}

.channel-category {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 0 8px 4px;
    color: var(--nexcord-text-muted);
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    cursor: pointer;
}

.channel-category i {
    font-size: 10px;
    transition: transform 0.2s ease;
}

.channel-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 6px 8px;
    margin: 1px 0;
    border-radius: 4px;
    color: var(--nexcord-text-muted);
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
}

.channel-item:hover {
    background: rgba(255, 255, 255, 0.05);
    color: var(--nexcord-text);
}

.channel-item.active {
    background: rgba(124, 58, 237, 0.2);
    color: var(--nexcord-text);
}

.channel-item i {
    font-size: 16px;
    width: 20px;
    text-align: center;
}

.notification-badge {
    background: var(--nexcord-danger);
    color: white;
    font-size: 12px;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 8px;
    margin-left: auto;
}

/* User Panel */
.user-panel {
    margin-top: auto;
    padding: 8px;
    background: rgba(0, 0, 0, 0.2);
    display: flex;
    align-items: center;
    gap: 8px;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 1;
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: var(--nexcord-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.user-avatar i {
    font-size: 14px;
    color: white;
}

.status-indicator {
    position: absolute;
    bottom: -2px;
    right: -2px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid var(--nexcord-dark-light);
}

.status-indicator.online {
    background: var(--nexcord-success);
}

.user-details {
    flex: 1;
    min-width: 0;
}

.username {
    color: var(--nexcord-text);
    font-size: 14px;
    font-weight: 600;
    line-height: 1.2;
}

.user-tag {
    color: var(--nexcord-text-muted);
    font-size: 12px;
    line-height: 1.2;
}

.user-controls {
    display: flex;
    gap: 4px;
}

.control-btn {
    width: 32px;
    height: 32px;
    border: none;
    background: none;
    color: var(--nexcord-text-muted);
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.control-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--nexcord-text);
}

.user-avatar-small {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: var(--nexcord-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    color: white;
}

.user-avatar-small img {
    width: 100%;
    height: 100%;
    border-radius: inherit;
    object-fit: cover;
}

/* Chat Area */
.chat-area {
    flex: 1;
    background: var(--nexcord-dark-lighter);
    display: flex;
    flex-direction: column;
}

.chat-header {
    height: 48px;
    padding: 0 16px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: var(--nexcord-dark-lighter);
}

.channel-info {
    display: flex;
    align-items: center;
    gap: 8px;
}

.channel-info i {
    color: var(--nexcord-text-muted);
    font-size: 20px;
}

.channel-name {
    color: var(--nexcord-text);
    font-size: 16px;
    font-weight: 600;
}

.chat-controls {
    display: flex;
    gap: 16px;
}

.chat-content {
    flex: 1;
    overflow-y: auto;
    padding: 16px;
}

.friends-content {
    height: 100%;
}

.friends-header {
    margin-bottom: 16px;
}

.friends-tabs {
    display: flex;
    align-items: center;
    gap: 32px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding-bottom: 16px;
}

.friends-tab {
    background: none;
    border: none;
    color: var(--nexcord-text-muted);
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    padding: 8px 0;
    position: relative;
    transition: color 0.2s ease;
}

.friends-tab:hover {
    color: var(--nexcord-text);
}

.friends-tab.active {
    color: var(--nexcord-text);
}

.friends-tab.active::after {
    content: '';
    position: absolute;
    bottom: -17px;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--nexcord-primary);
    border-radius: 1px;
}

.add-friend-btn {
    background: var(--nexcord-success);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    margin-left: auto;
    transition: background 0.2s ease;
}

.add-friend-btn:hover {
    background: #0ea572;
}

.friends-list-container {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
}

.empty-state {
    text-align: center;
    max-width: 400px;
}

.empty-icon {
    font-size: 120px;
    color: var(--nexcord-text-muted);
    margin-bottom: 32px;
    opacity: 0.3;
}

.empty-state h3 {
    color: var(--nexcord-text);
    font-size: 20px;
    margin-bottom: 8px;
}

.empty-state p {
    color: var(--nexcord-text-muted);
    font-size: 16px;
    line-height: 1.5;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--nexcord-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.activity-content p {
    color: var(--nexcord-text);
    margin-bottom: 0.25rem;
}

.activity-content small {
    color: var(--nexcord-text-muted);
}

.action-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.action-card {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 0.75rem;
    padding: 1.5rem;
    text-align: center;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.action-card:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.08);
}

.action-card i {
    font-size: 2rem;
    color: var(--nexcord-primary);
    margin-bottom: 1rem;
}

.action-card h4 {
    color: var(--nexcord-text);
    margin-bottom: 0.5rem;
}

.action-card p {
    color: var(--nexcord-text-muted);
    margin-bottom: 1rem;
    font-size: 0.9rem;
}

.btn-action {
    background: var(--nexcord-primary);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    font-weight: 600;
    transition: all 0.3s ease;
    cursor: pointer;
}

.btn-action:hover {
    background: var(--nexcord-primary-dark);
    transform: translateY(-2px);
}

.friend-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.75rem 0;
}

.friend-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--nexcord-text-muted);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.friend-info {
    flex: 1;
}

.friend-name {
    display: block;
    color: var(--nexcord-text);
    font-weight: 600;
}

.friend-status {
    display: block;
    color: var(--nexcord-text-muted);
    font-size: 0.8rem;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.stat-item:last-child {
    border-bottom: none;
}

.stat-label {
    color: var(--nexcord-text-muted);
}

.stat-value {
    color: var(--nexcord-text);
    font-weight: 600;
}

/* Updated Game Cards with Logos */
.game-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(124, 58, 237, 0.2);
    border-radius: 1rem;
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    height: 350px;
    display: flex;
    flex-direction: column;
    backdrop-filter: blur(10px);
}

.game-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, transparent 0%, rgba(124, 58, 237, 0.1) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.game-card:hover::before {
    opacity: 1;
}

.game-card:hover {
    transform: translateY(-10px);
    border-color: var(--nexcord-primary);
    box-shadow: 0 20px 40px rgba(124, 58, 237, 0.3);
}

.game-logo-container {
    height: 200px;
    overflow: hidden;
    border-radius: 0.75rem 0.75rem 0 0;
    position: relative;
}

.game-logo-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.game-card:hover .game-logo-img {
    transform: scale(1.1);
}

.game-info {
    padding: 1.5rem;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    position: relative;
    z-index: 2;
}

.game-info h3 {
    color: var(--nexcord-text);
    margin-bottom: 0.5rem;
    font-weight: 700;
    font-size: 1.25rem;
}

.game-info p {
    color: var(--nexcord-text-muted);
    margin-bottom: 0;
    font-size: 0.9rem;
}

.game-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 1.5rem;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.9));
    transform: translateY(100%);
    transition: transform 0.3s ease;
}

.game-card:hover .game-overlay {
    transform: translateY(0);
}

.btn-join {
    background: linear-gradient(135deg, var(--nexcord-primary), var(--nexcord-secondary));
    color: white;
    border: none;
    padding: 0.75rem 2rem;
    border-radius: 2rem;
    font-weight: 600;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(124, 58, 237, 0.3);
    width: 100%;
}

.btn-join:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(124, 58, 237, 0.4);
    color: white;
    text-decoration: none;
}



/* Member List (Right sidebar) */
.member-list {
    width: 240px;
    background: var(--nexcord-dark-lighter);
    padding: 16px 8px;
    overflow-y: auto;
}

.member-section {
    margin-bottom: 24px;
}

.member-category {
    padding: 0 8px 8px;
    color: var(--nexcord-text-muted);
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.member-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px;
    border-radius: 4px;
    cursor: pointer;
    transition: background 0.2s ease;
}

.member-item:hover {
    background: rgba(255, 255, 255, 0.05);
}

.member-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: var(--nexcord-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.member-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.member-avatar i {
    font-size: 14px;
    color: white;
}

.member-info {
    flex: 1;
    min-width: 0;
}

.member-name {
    color: var(--nexcord-text);
    font-size: 14px;
    font-weight: 500;
    line-height: 1.2;
    margin-bottom: 2px;
}

.member-activity {
    color: var(--nexcord-text-muted);
    font-size: 12px;
    line-height: 1.2;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .member-list {
        display: none;
    }
}

/* Friend System Styles - Discord-like */
.friends-list {
    display: flex !important;
    flex-direction: column !important;
    gap: 0 !important;
    padding: 8px 20px !important;
    background: transparent !important;
}

.friend-item {
    display: flex !important;
    align-items: center !important;
    gap: 12px !important;
    padding: 10px 8px !important;
    border-radius: 8px !important;
    background: transparent !important;
    transition: all 0.15s ease !important;
    cursor: pointer !important;
    border: 2px solid transparent !important;
    margin: 1px 0 !important;
    min-height: 62px !important;
    position: relative !important;
}

.friend-item:hover {
    background: rgba(79, 84, 92, 0.16);
    border-radius: 8px;
}

.friend-avatar {
    position: relative;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    overflow: hidden;
    flex-shrink: 0;
}

.friend-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.avatar-placeholder {
    width: 32px;
    height: 32px;
    background: #5865f2;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 14px;
}

.status-indicator {
    position: absolute;
    bottom: -2px;
    right: -2px;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    border: 2px solid #2f3136;
    background: #747f8d;
}

.status-indicator.online {
    background: #3ba55c;
}

.status-indicator.offline {
    background: #747f8d;
}

.friend-info {
    flex: 1;
    min-width: 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.friend-name {
    color: #f2f3f5;
    font-weight: 600;
    font-size: 16px;
    margin-bottom: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 1.2;
}

.friend-status {
    color: #b5bac1;
    font-size: 13px;
    margin-top: 2px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 1.2;
}

.friend-actions {
    display: flex;
    gap: 10px;
    opacity: 0;
    transition: opacity 0.15s ease;
    margin-left: auto;
}

.friend-item:hover .friend-actions {
    opacity: 1;
}

.action-btn {
    background: #4f545c;
    border: none;
    border-radius: 3px;
    color: #b5bac1;
    cursor: pointer;
    padding: 0;
    transition: all 0.15s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    font-size: 18px;
}

.action-btn:hover {
    background: #5865f2;
    color: white;
}

.action-btn.chat-btn:hover {
    background: #3ba55c;
}

.action-btn.more-btn:hover {
    background: #4f545c;
}

.accept-btn:hover {
    background: #3ba55c;
}

.decline-btn:hover {
    background: #ed4245;
}

/* Discord-like friend list header */
.friends-header {
    padding: 16px 20px 8px 20px;
    border-bottom: 1px solid rgba(79, 84, 92, 0.48);
    margin-bottom: 8px;
}

.friends-tabs {
    display: flex;
    align-items: center;
    gap: 32px;
}

.friends-tab {
    background: none;
    border: none;
    color: #b5bac1;
    font-size: 16px;
    font-weight: 500;
    padding: 8px 0;
    cursor: pointer;
    transition: color 0.15s ease;
    position: relative;
}

.friends-tab.active {
    color: #f2f3f5;
}

.friends-tab.active::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    right: 0;
    height: 2px;
    background: #f2f3f5;
    border-radius: 1px;
}

.friends-tab:hover:not(.active) {
    color: #dcddde;
}

.add-friend-btn {
    background: #3ba55c;
    color: white;
    padding: 8px 16px;
    border-radius: 3px;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    transition: background 0.15s ease;
    margin-left: auto;
}

.add-friend-btn:hover {
    background: #2d7d32;
    color: white;
    text-decoration: none;
}

/* Friends list scrollbar styling */
.friends-list-container {
    overflow-y: auto;
    max-height: calc(100vh - 200px);
}

.friends-list-container::-webkit-scrollbar {
    width: 8px;
}

.friends-list-container::-webkit-scrollbar-track {
    background: transparent;
}

.friends-list-container::-webkit-scrollbar-thumb {
    background: #202225;
    border-radius: 4px;
}

.friends-list-container::-webkit-scrollbar-thumb:hover {
    background: #36393f;
}

/* Discord-like Direct Messages List */
.dm-list {
    display: flex;
    flex-direction: column;
    gap: 2px;
    padding: 8px;
}

.dm-friend-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px;
    border-radius: 4px;
    background: transparent;
    transition: all 0.15s ease;
    cursor: pointer;
    color: #b5bac1;
    text-decoration: none;
    min-height: 42px;
}

.dm-friend-item:hover {
    background: rgba(79, 84, 92, 0.16);
    color: #dcddde;
    text-decoration: none;
}

.dm-friend-item.active {
    background: rgba(79, 84, 92, 0.32);
    color: #f2f3f5;
}

.dm-friend-avatar {
    position: relative;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    overflow: hidden;
    flex-shrink: 0;
}

.dm-friend-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.avatar-placeholder-small {
    width: 32px;
    height: 32px;
    background: #5865f2;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 14px;
}

.status-indicator-small {
    position: absolute;
    bottom: -2px;
    right: -2px;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    border: 2px solid #2f3136;
    background: #747f8d;
}

.status-indicator-small.online {
    background: #3ba55c;
}

.status-indicator-small.offline {
    background: #747f8d;
}

.dm-friend-info {
    flex: 1;
    min-width: 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.dm-friend-name {
    font-weight: 500;
    font-size: 14px;
    line-height: 1.2;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-bottom: 1px;
}

.dm-friend-status {
    font-size: 12px;
    line-height: 1.2;
    opacity: 0.8;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Force Discord-like styling with higher specificity */
.friends-content .friends-list-container .friends-list .friend-item {
    display: flex !important;
    align-items: center !important;
    gap: 12px !important;
    padding: 10px 8px !important;
    border-radius: 8px !important;
    background: transparent !important;
    transition: all 0.15s ease !important;
    cursor: pointer !important;
    border: 2px solid transparent !important;
    margin: 1px 0 !important;
    min-height: 62px !important;
    position: relative !important;
}

.friends-content .friends-list-container .friends-list .friend-item:hover {
    background: rgba(79, 84, 92, 0.16) !important;
    border-radius: 8px !important;
}

.friends-content .friends-list-container .friends-list {
    display: flex !important;
    flex-direction: column !important;
    gap: 0 !important;
    padding: 8px 20px !important;
    background: transparent !important;
}

/* Pending request specific styling */
.pending-request .friend-actions {
    opacity: 1;
}

.pending-request .friend-status {
    color: #faa61a;
    font-weight: 500;
}

.tab-content {
    display: none !important;
}

.tab-content.active {
    display: block !important;
}

.view-all-friends {
    text-align: center;
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: 16px;
}

.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: all 0.2s ease;
}

.btn-primary {
    background: var(--nexcord-primary);
    color: white;
}

.btn-primary:hover {
    background: var(--nexcord-primary-dark);
}

.dm-item {
    text-decoration: none;
    color: inherit;
}

.user-avatar-small {
    position: relative;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    overflow: hidden;
}

.user-avatar-small img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.status-indicator-small {
    position: absolute;
    bottom: -2px;
    right: -2px;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    border: 2px solid var(--nexcord-dark-lighter);
}

.status-indicator-small.online {
    background: #43b581;
}

.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 20px;
    border-radius: 8px;
    color: white;
    font-weight: 600;
    z-index: 1000;
    transform: translateX(100%);
    transition: transform 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.notification.show {
    transform: translateX(0);
}

.notification.success {
    background: #43b581;
}

.notification.error {
    background: #f04747;
}

.notification.info {
    background: #7289da;
}

@media (max-width: 768px) {
    .discord-layout {
        flex-direction: column;
        height: auto;
    }

    .server-list {
        width: 100%;
        height: 60px;
        flex-direction: row;
        padding: 8px 12px;
        overflow-x: auto;
    }

    .channel-list {
        width: 100%;
        height: auto;
        max-height: 200px;
    }

    .chat-area {
        height: calc(100vh - 200px);
    }
}

/* Add Friend Styles */
.add-friend-container {
    padding: 20px;
    max-width: 600px;
}

.add-friend-header {
    margin-bottom: 20px;
}

.add-friend-header h3 {
    color: var(--nexcord-text);
    margin: 0 0 8px 0;
    font-size: 20px;
    font-weight: 600;
}

.add-friend-header p {
    color: var(--nexcord-text-muted);
    margin: 0;
    font-size: 14px;
}

.add-friend-form {
    margin-bottom: 30px;
}

.search-input-container {
    display: flex;
    gap: 10px;
    margin-bottom: 8px;
}

.friend-search-input {
    flex: 1;
    background: var(--nexcord-dark);
    border: 1px solid var(--nexcord-border);
    color: var(--nexcord-text);
    padding: 12px 16px;
    border-radius: 6px;
    font-size: 14px;
}

.friend-search-input:focus {
    outline: none;
    border-color: var(--nexcord-primary);
    box-shadow: 0 0 0 2px rgba(114, 137, 218, 0.2);
}

.friend-search-input::placeholder {
    color: var(--nexcord-text-muted);
}

.search-btn {
    padding: 12px 20px;
    font-size: 14px;
    font-weight: 500;
    white-space: nowrap;
}

.search-help {
    margin-top: 8px;
}

.search-help small {
    display: flex;
    align-items: center;
    gap: 6px;
    color: var(--nexcord-text-muted);
    font-size: 12px;
}

.search-results {
    margin-bottom: 30px;
}

.search-results h4 {
    color: var(--nexcord-text);
    margin: 0 0 15px 0;
    font-size: 16px;
    font-weight: 600;
}

.results-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.add-friend-suggestions h4 {
    color: var(--nexcord-text);
    margin: 0 0 5px 0;
    font-size: 16px;
    font-weight: 600;
}

.suggestions-subtitle {
    color: var(--nexcord-text-muted);
    margin: 0 0 15px 0;
    font-size: 14px;
}

.suggestions-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.suggestion-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    background: var(--nexcord-dark);
    border-radius: 6px;
    border: 1px solid var(--nexcord-border);
}

.suggestion-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--nexcord-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 16px;
}

.suggestion-info {
    flex: 1;
}

.suggestion-name {
    color: var(--nexcord-text);
    font-weight: 500;
    font-size: 14px;
    margin-bottom: 2px;
}

.suggestion-mutual {
    color: var(--nexcord-text-muted);
    font-size: 12px;
}

.search-result-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    background: var(--nexcord-dark);
    border-radius: 6px;
    border: 1px solid var(--nexcord-border);
    margin-bottom: 8px;
}

.result-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--nexcord-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 16px;
    overflow: hidden;
}

.result-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.result-info {
    flex: 1;
}

.result-name {
    color: var(--nexcord-text);
    font-weight: 500;
    font-size: 14px;
    margin-bottom: 2px;
}

.result-email {
    color: var(--nexcord-text-muted);
    font-size: 12px;
    margin-bottom: 2px;
}

.result-status {
    color: var(--nexcord-text-muted);
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 4px;
}

.result-status .fa-circle.online {
    color: #43b581;
}

.result-status .fa-circle.offline {
    color: #747f8d;
}

.result-actions {
    display: flex;
    align-items: center;
}

.status-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

.status-badge.pending {
    background: rgba(255, 193, 7, 0.2);
    color: #ffc107;
}

.status-badge.friends {
    background: rgba(40, 167, 69, 0.2);
    color: #28a745;
}

.status-badge.incoming {
    background: rgba(255, 193, 7, 0.2);
    color: #ffc107;
}

.loading-state, .error-state, .empty-state {
    text-align: center;
    padding: 20px;
    color: var(--nexcord-text-muted);
}

.loading-state i, .error-state i, .empty-state i {
    font-size: 24px;
    margin-bottom: 8px;
    display: block;
}

.error-state {
    color: #dc3545;
}

/* Alert Messages */
.alert {
    padding: 12px 16px;
    margin-bottom: 15px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    font-weight: 500;
}

.alert-success {
    background: rgba(40, 167, 69, 0.1);
    color: #28a745;
    border: 1px solid rgba(40, 167, 69, 0.2);
}

.alert-danger {
    background: rgba(220, 53, 69, 0.1);
    color: #dc3545;
    border: 1px solid rgba(220, 53, 69, 0.2);
}

.alert i {
    font-size: 16px;
}

/* Events View Styles */
.events-content {
    padding: 20px;
    background: var(--nexcord-dark-light);
    height: 100%;
    overflow-y: auto;
}

.events-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid var(--nexcord-border);
}

.events-title h2 {
    color: var(--nexcord-text);
    margin: 0;
    font-size: 24px;
    font-weight: 600;
}

.events-title p {
    color: var(--nexcord-text-muted);
    margin: 5px 0 0 0;
    font-size: 14px;
}

.events-tabs {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    border-bottom: 1px solid var(--nexcord-border);
}

.events-tab {
    background: none;
    border: none;
    color: var(--nexcord-text-muted);
    padding: 10px 15px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    border-bottom: 2px solid transparent;
    transition: all 0.2s ease;
}

.events-tab:hover {
    color: var(--nexcord-text);
}

.events-tab.active {
    color: var(--nexcord-primary);
    border-bottom-color: var(--nexcord-primary);
}

.events-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 20px;
}

.event-card {
    background: var(--nexcord-dark);
    border-radius: 8px;
    overflow: hidden;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    border: 1px solid var(--nexcord-border);
}

.event-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.event-image {
    position: relative;
    height: 150px;
    overflow: hidden;
}

.event-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.event-date {
    position: absolute;
    top: 10px;
    right: 10px;
    background: var(--nexcord-primary);
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    text-align: center;
    font-weight: 600;
    font-size: 12px;
}

.event-date .month {
    display: block;
    font-size: 10px;
    opacity: 0.9;
}

.event-date .day {
    display: block;
    font-size: 16px;
    line-height: 1;
}

.event-info {
    padding: 15px;
}

.event-info h3 {
    color: var(--nexcord-text);
    margin: 0 0 8px 0;
    font-size: 16px;
    font-weight: 600;
}

.event-description {
    color: var(--nexcord-text-muted);
    font-size: 14px;
    line-height: 1.4;
    margin: 0 0 12px 0;
}

.event-details {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
    font-size: 12px;
    color: var(--nexcord-text-muted);
}

.event-details span {
    display: flex;
    align-items: center;
    gap: 5px;
}

.event-actions {
    display: flex;
    gap: 8px;
}

.event-actions .btn {
    flex: 1;
    padding: 8px 12px;
    font-size: 12px;
    border-radius: 4px;
}

/* Chat View Styles */
.chat-view {
    display: flex;
    flex-direction: column;
    height: 100%;
    background: #36393f;
}

.chat-messages-container {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
}

.chat-messages {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.message {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    padding: 8px 0;
}

.message-own {
    flex-direction: row-reverse;
}

.message-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
    flex-shrink: 0;
}

.message-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.avatar-placeholder-small {
    width: 40px;
    height: 40px;
    background: #5865f2;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 16px;
}

.message-content {
    max-width: 70%;
    background: #40444b;
    border-radius: 18px;
    padding: 12px 16px;
    color: #dcddde;
}

.message-own .message-content {
    background: #5865f2;
    color: white;
}

.message-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 4px;
}

.message-sender {
    font-weight: 600;
    font-size: 14px;
    color: #ffffff;
}

.message-time {
    font-size: 12px;
    color: #72767d;
}

.message-time-own {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.7);
    text-align: right;
    margin-top: 4px;
}

.message-text {
    line-height: 1.4;
    word-wrap: break-word;
}

.chat-input-container {
    padding: 20px;
    background: #40444b;
    border-top: 1px solid #32353b;
}

.message-form {
    display: flex;
    align-items: center;
}

.message-input-wrapper {
    display: flex;
    align-items: center;
    background: #484c52;
    border-radius: 24px;
    padding: 12px 16px;
    width: 100%;
}

#message-input {
    flex: 1;
    background: none;
    border: none;
    color: #dcddde;
    font-size: 14px;
    outline: none;
    resize: none;
}

#message-input::placeholder {
    color: #72767d;
}

.send-button {
    background: none;
    border: none;
    color: #72767d;
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: all 0.2s ease;
    margin-left: 8px;
}

.send-button:hover {
    color: #5865f2;
    background: rgba(88, 101, 242, 0.1);
}

.send-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Friend status in header */
.friend-status {
    font-size: 12px;
    color: #72767d;
    margin-left: 8px;
}

/* Make chat buttons clickable */
.chat-btn {
    cursor: pointer;
    transition: all 0.2s ease;
}

.chat-btn:hover {
    background: rgba(79, 84, 92, 0.32);
}

.dm-item.chat-btn {
    text-decoration: none;
    color: inherit;
}

.dm-item.chat-btn:hover {
    text-decoration: none;
    color: inherit;
}