using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.SignalR;
using Nexcord.Data;
using Nexcord.Models;
using Nexcord.ViewModels;
using Nexcord.Hubs;

namespace Nexcord.Controllers
{
    [Authorize]
    public class FriendController : Controller
    {
        private readonly ApplicationDbContext _context;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly IHubContext<ChatHub> _hubContext;

        public FriendController(ApplicationDbContext context, UserManager<ApplicationUser> userManager, IHubContext<ChatHub> hubContext)
        {
            _context = context;
            _userManager = userManager;
            _hubContext = hubContext;
        }

        // GET: Friend/Search
        public IActionResult Search()
        {
            return View(new UserSearchViewModel());
        }

        // POST: Friend/Search
        [HttpPost]
        public async Task<IActionResult> Search(UserSearchViewModel model)
        {
            if (!ModelState.IsValid)
            {
                return View(model);
            }

            var currentUserId = _userManager.GetUserId(User);
            if (currentUserId == null)
            {
                return RedirectToAction("Login", "Account");
            }
            var searchTerm = model.SearchTerm.Trim().ToLower();

            // Search users by username or email
            var users = await _context.Users
                .Include(u => u.Profile)
                .Where(u => u.Id != currentUserId &&
                           (u.UserName!.ToLower().Contains(searchTerm) ||
                            u.Email!.ToLower().Contains(searchTerm) ||
                            (u.Profile != null && u.Profile.DisplayName != null &&
                             u.Profile.DisplayName.ToLower().Contains(searchTerm))))
                .Take(20)
                .ToListAsync();

            // Get friendship statuses
            var userIds = users.Select(u => u.Id).ToList();
            var friendships = await _context.Friendships
                .Where(f => (f.RequesterId == currentUserId && userIds.Contains(f.AddresseeId)) ||
                           (f.AddresseeId == currentUserId && userIds.Contains(f.RequesterId)))
                .ToListAsync();

            model.Results = users.Select(user =>
            {
                var friendship = friendships.FirstOrDefault(f =>
                    (f.RequesterId == currentUserId && f.AddresseeId == user.Id) ||
                    (f.AddresseeId == currentUserId && f.RequesterId == user.Id));

                var friendStatus = GetFriendStatus(friendship, currentUserId, user.Id);

                return new UserSearchResultViewModel
                {
                    Id = user.Id,
                    Username = user.UserName ?? "",
                    Email = user.Email ?? "",
                    DisplayName = user.DisplayName,
                    FullName = user.FullName,
                    AvatarUrl = user.Profile?.AvatarUrl,
                    IsOnline = user.IsOnline,
                    LastActive = user.LastActive,
                    FriendStatus = friendStatus,
                    FriendshipId = friendship?.Id,
                    IsCurrentUser = false
                };
            }).ToList();

            model.HasSearched = true;
            return View(model);
        }

        // POST: Friend/SearchApi - API endpoint for AJAX search
        [HttpPost]
        public async Task<IActionResult> SearchApi([FromBody] SearchApiRequest request)
        {
            if (string.IsNullOrWhiteSpace(request.SearchTerm))
            {
                return Json(new List<UserSearchResultViewModel>());
            }

            var currentUserId = _userManager.GetUserId(User);
            if (currentUserId == null)
            {
                return Json(new List<UserSearchResultViewModel>());
            }

            var searchTerm = request.SearchTerm.Trim().ToLower();

            // Search users by username or email
            var users = await _context.Users
                .Include(u => u.Profile)
                .Where(u => u.Id != currentUserId &&
                           (u.UserName!.ToLower().Contains(searchTerm) ||
                            u.Email!.ToLower().Contains(searchTerm) ||
                            (u.Profile != null && u.Profile.DisplayName != null &&
                             u.Profile.DisplayName.ToLower().Contains(searchTerm))))
                .Take(10) // Limit to 10 results for API
                .ToListAsync();

            // Get existing friendships
            var friendships = await _context.Friendships
                .Where(f => (f.RequesterId == currentUserId || f.AddresseeId == currentUserId))
                .ToListAsync();

            var results = users.Select(user =>
            {
                var friendship = friendships.FirstOrDefault(f =>
                    (f.RequesterId == currentUserId && f.AddresseeId == user.Id) ||
                    (f.AddresseeId == currentUserId && f.RequesterId == user.Id));

                var friendStatus = GetFriendStatus(friendship, currentUserId, user.Id);

                return new UserSearchResultViewModel
                {
                    Id = user.Id,
                    Username = user.UserName ?? "",
                    Email = user.Email ?? "",
                    DisplayName = user.DisplayName,
                    FullName = user.FullName,
                    AvatarUrl = user.Profile?.AvatarUrl,
                    IsOnline = user.IsOnline,
                    LastActive = user.LastActive,
                    FriendStatus = friendStatus,
                    FriendshipId = friendship?.Id,
                    IsCurrentUser = false
                };
            }).ToList();

            return Json(results);
        }

        // POST: Friend/SendRequest
        [HttpPost]
        public async Task<IActionResult> SendRequest([FromBody] SendFriendRequestViewModel model)
        {
            if (!ModelState.IsValid)
            {
                return Json(new { success = false, message = "Invalid request data." });
            }

            var currentUserId = _userManager.GetUserId(User);
            
            if (currentUserId == model.UserId)
            {
                return Json(new { success = false, message = "You cannot send a friend request to yourself." });
            }

            // Check if user exists
            var targetUser = await _context.Users.FindAsync(model.UserId);
            if (targetUser == null)
            {
                return Json(new { success = false, message = "User not found." });
            }

            // Check if friendship already exists
            var existingFriendship = await _context.Friendships
                .FirstOrDefaultAsync(f => 
                    (f.RequesterId == currentUserId && f.AddresseeId == model.UserId) ||
                    (f.AddresseeId == currentUserId && f.RequesterId == model.UserId));

            if (existingFriendship != null)
            {
                var message = existingFriendship.Status switch
                {
                    FriendshipStatus.Pending => existingFriendship.RequesterId == currentUserId 
                        ? "Friend request already sent." 
                        : "This user has already sent you a friend request.",
                    FriendshipStatus.Accepted => "You are already friends with this user.",
                    FriendshipStatus.Blocked => "Cannot send friend request to this user.",
                    _ => "Friend request cannot be sent."
                };
                return Json(new { success = false, message });
            }

            // Create new friendship request
            var friendship = new Friendship
            {
                RequesterId = currentUserId!,
                AddresseeId = model.UserId,
                Status = FriendshipStatus.Pending,
                CreatedAt = DateTime.UtcNow
            };

            _context.Friendships.Add(friendship);
            await _context.SaveChangesAsync();

            // Send real-time notification to the target user
            var currentUser = await _context.Users
                .Include(u => u.Profile)
                .FirstOrDefaultAsync(u => u.Id == currentUserId);

            if (currentUser != null)
            {
                var notificationData = new
                {
                    friendshipId = friendship.Id,
                    requester = new
                    {
                        id = currentUser.Id,
                        username = currentUser.UserName,
                        displayName = currentUser.DisplayName,
                        fullName = currentUser.FullName,
                        avatarUrl = currentUser.Profile?.AvatarUrl,
                        isOnline = currentUser.IsOnline
                    },
                    requestedAt = friendship.CreatedAt
                };

                await _hubContext.Clients.User(model.UserId).SendAsync("FriendRequestReceived", notificationData);
            }

            return Json(new { success = true, message = "Friend request sent successfully!" });
        }

        // POST: Friend/AcceptRequest
        [HttpPost]
        public async Task<IActionResult> AcceptRequest(int friendshipId)
        {
            var currentUserId = _userManager.GetUserId(User);
            
            var friendship = await _context.Friendships
                .Include(f => f.Requester)
                .FirstOrDefaultAsync(f => f.Id == friendshipId && 
                                        f.AddresseeId == currentUserId && 
                                        f.Status == FriendshipStatus.Pending);

            if (friendship == null)
            {
                return Json(new { success = false, message = "Friend request not found." });
            }

            friendship.Status = FriendshipStatus.Accepted;
            friendship.AcceptedAt = DateTime.UtcNow;
            friendship.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            // Send real-time notification to the requester
            var currentUser = await _context.Users
                .Include(u => u.Profile)
                .FirstOrDefaultAsync(u => u.Id == currentUserId);

            if (currentUser != null)
            {
                var notificationData = new
                {
                    friend = new
                    {
                        id = currentUser.Id,
                        username = currentUser.UserName,
                        displayName = currentUser.DisplayName,
                        fullName = currentUser.FullName,
                        avatarUrl = currentUser.Profile?.AvatarUrl,
                        isOnline = currentUser.IsOnline
                    },
                    acceptedAt = friendship.AcceptedAt
                };

                await _hubContext.Clients.User(friendship.RequesterId).SendAsync("FriendRequestAccepted", notificationData);
            }

            return Json(new { success = true, message = $"You are now friends with {friendship.Requester.DisplayName}!" });
        }

        // POST: Friend/DeclineRequest
        [HttpPost]
        public async Task<IActionResult> DeclineRequest(int friendshipId)
        {
            var currentUserId = _userManager.GetUserId(User);
            
            var friendship = await _context.Friendships
                .FirstOrDefaultAsync(f => f.Id == friendshipId && 
                                        f.AddresseeId == currentUserId && 
                                        f.Status == FriendshipStatus.Pending);

            if (friendship == null)
            {
                return Json(new { success = false, message = "Friend request not found." });
            }

            friendship.Status = FriendshipStatus.Declined;
            friendship.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            return Json(new { success = true, message = "Friend request declined." });
        }

        // POST: Friend/RemoveFriend
        [HttpPost]
        public async Task<IActionResult> RemoveFriend(string friendId)
        {
            var currentUserId = _userManager.GetUserId(User);
            
            var friendship = await _context.Friendships
                .FirstOrDefaultAsync(f => 
                    ((f.RequesterId == currentUserId && f.AddresseeId == friendId) ||
                     (f.AddresseeId == currentUserId && f.RequesterId == friendId)) &&
                    f.Status == FriendshipStatus.Accepted);

            if (friendship == null)
            {
                return Json(new { success = false, message = "Friendship not found." });
            }

            _context.Friendships.Remove(friendship);
            await _context.SaveChangesAsync();

            return Json(new { success = true, message = "Friend removed successfully." });
        }

        // GET: Friend/List
        public async Task<IActionResult> List()
        {
            var currentUserId = _userManager.GetUserId(User);
            
            var friendships = await _context.Friendships
                .Include(f => f.Requester).ThenInclude(u => u.Profile)
                .Include(f => f.Addressee).ThenInclude(u => u.Profile)
                .Where(f => (f.RequesterId == currentUserId || f.AddresseeId == currentUserId))
                .ToListAsync();

            var model = new FriendListViewModel();

            // Get accepted friends
            var acceptedFriendships = friendships.Where(f => f.Status == FriendshipStatus.Accepted).ToList();
            model.Friends = acceptedFriendships.Select(f =>
            {
                var friend = f.GetOtherUser(currentUserId!);
                return new FriendViewModel
                {
                    Id = friend.Id,
                    Username = friend.UserName ?? "",
                    DisplayName = friend.DisplayName,
                    FullName = friend.FullName,
                    AvatarUrl = friend.Profile?.AvatarUrl,
                    IsOnline = friend.IsOnline,
                    LastActive = friend.LastActive,
                    FriendsSince = f.AcceptedAt ?? f.CreatedAt
                };
            }).OrderBy(f => f.Username).ToList();

            // Get pending incoming requests
            model.PendingRequests = friendships
                .Where(f => f.Status == FriendshipStatus.Pending && f.AddresseeId == currentUserId)
                .Select(f => new FriendRequestViewModel
                {
                    FriendshipId = f.Id,
                    UserId = f.RequesterId,
                    Username = f.Requester.UserName ?? "",
                    DisplayName = f.Requester.DisplayName,
                    FullName = f.Requester.FullName,
                    AvatarUrl = f.Requester.Profile?.AvatarUrl,
                    IsOnline = f.Requester.IsOnline,
                    RequestedAt = f.CreatedAt,
                    IsIncoming = true
                }).OrderByDescending(r => r.RequestedAt).ToList();

            // Get sent requests
            model.SentRequests = friendships
                .Where(f => f.Status == FriendshipStatus.Pending && f.RequesterId == currentUserId)
                .Select(f => new FriendRequestViewModel
                {
                    FriendshipId = f.Id,
                    UserId = f.AddresseeId,
                    Username = f.Addressee.UserName ?? "",
                    DisplayName = f.Addressee.DisplayName,
                    FullName = f.Addressee.FullName,
                    AvatarUrl = f.Addressee.Profile?.AvatarUrl,
                    IsOnline = f.Addressee.IsOnline,
                    RequestedAt = f.CreatedAt,
                    IsIncoming = false
                }).OrderByDescending(r => r.RequestedAt).ToList();

            model.OnlineFriendsCount = model.Friends.Count(f => f.IsOnline);
            model.TotalFriendsCount = model.Friends.Count;
            model.PendingRequestsCount = model.PendingRequests.Count;

            return View(model);
        }

        // GET: Friend/Chat/{friendId}
        public async Task<IActionResult> Chat(string friendId)
        {
            var currentUserId = _userManager.GetUserId(User);

            // Verify friendship exists and is accepted
            var friendship = await _context.Friendships
                .FirstOrDefaultAsync(f =>
                    ((f.RequesterId == currentUserId && f.AddresseeId == friendId) ||
                     (f.AddresseeId == currentUserId && f.RequesterId == friendId)) &&
                    f.Status == FriendshipStatus.Accepted);

            if (friendship == null)
            {
                return NotFound("Friendship not found or not accepted.");
            }

            var friend = await _context.Users
                .Include(u => u.Profile)
                .FirstOrDefaultAsync(u => u.Id == friendId);

            if (friend == null)
            {
                return NotFound("User not found.");
            }

            // Get recent messages
            var messages = await _context.DirectMessages
                .Where(dm => (dm.SenderId == currentUserId && dm.ReceiverId == friendId) ||
                            (dm.SenderId == friendId && dm.ReceiverId == currentUserId))
                .OrderByDescending(dm => dm.CreatedAt)
                .Take(50)
                .Include(dm => dm.Sender)
                .ToListAsync();

            // Mark messages as read
            var unreadMessages = messages.Where(m => m.ReceiverId == currentUserId && !m.IsRead).ToList();
            foreach (var message in unreadMessages)
            {
                message.IsRead = true;
            }
            if (unreadMessages.Any())
            {
                await _context.SaveChangesAsync();
            }

            var model = new ChatViewModel
            {
                FriendId = friendId,
                FriendUsername = friend.UserName ?? "",
                FriendDisplayName = friend.DisplayName,
                FriendAvatarUrl = friend.Profile?.AvatarUrl,
                FriendIsOnline = friend.IsOnline,
                FriendLastActive = friend.LastActive,
                Messages = messages.OrderBy(m => m.CreatedAt).Select(m => new DirectMessageViewModel
                {
                    Id = m.Id,
                    Content = m.Content,
                    CreatedAt = m.CreatedAt,
                    EditedAt = m.EditedAt,
                    IsFromCurrentUser = m.SenderId == currentUserId,
                    SenderName = m.Sender.DisplayName,
                    SenderAvatarUrl = m.Sender.Profile?.AvatarUrl,
                    IsRead = m.IsRead
                }).ToList(),
                TotalMessages = messages.Count,
                HasMoreMessages = messages.Count >= 50
            };

            return View(model);
        }

        // GET: Friend/GetChatMessages
        [HttpGet]
        public async Task<IActionResult> GetChatMessages(string friendId)
        {
            var currentUserId = _userManager.GetUserId(User);

            // Verify friendship exists and is accepted
            var friendship = await _context.Friendships
                .FirstOrDefaultAsync(f =>
                    ((f.RequesterId == currentUserId && f.AddresseeId == friendId) ||
                     (f.AddresseeId == currentUserId && f.RequesterId == friendId)) &&
                    f.Status == FriendshipStatus.Accepted);

            if (friendship == null)
            {
                return Json(new { success = false, message = "Friendship not found or not accepted." });
            }

            // Get messages between current user and friend
            var messages = await _context.DirectMessages
                .Include(m => m.Sender)
                .ThenInclude(s => s.Profile)
                .Where(m => (m.SenderId == currentUserId && m.ReceiverId == friendId) ||
                           (m.SenderId == friendId && m.ReceiverId == currentUserId))
                .OrderBy(m => m.CreatedAt)
                .Take(50) // Limit to last 50 messages
                .Select(m => new
                {
                    id = m.Id,
                    content = m.Content,
                    createdAt = m.CreatedAt,
                    isFromCurrentUser = m.SenderId == currentUserId,
                    senderName = m.Sender.DisplayName,
                    senderAvatarUrl = m.Sender.Profile != null ? m.Sender.Profile.AvatarUrl : null
                })
                .ToListAsync();

            return Json(new { success = true, messages = messages });
        }

        // POST: Friend/SendMessage
        [HttpPost]
        public async Task<IActionResult> SendMessage([FromBody] SendMessageRequest request)
        {
            if (string.IsNullOrWhiteSpace(request.Content) || request.Content.Length > 2000)
            {
                return Json(new { success = false, message = "Invalid message content." });
            }

            var currentUserId = _userManager.GetUserId(User);

            // Verify friendship
            var friendship = await _context.Friendships
                .FirstOrDefaultAsync(f =>
                    ((f.RequesterId == currentUserId && f.AddresseeId == request.FriendId) ||
                     (f.AddresseeId == currentUserId && f.RequesterId == request.FriendId)) &&
                    f.Status == FriendshipStatus.Accepted);

            if (friendship == null)
            {
                return Json(new { success = false, message = "Cannot send message to this user." });
            }

            var message = new DirectMessage
            {
                SenderId = currentUserId!,
                ReceiverId = request.FriendId,
                Content = request.Content.Trim(),
                CreatedAt = DateTime.UtcNow
            };

            _context.DirectMessages.Add(message);
            await _context.SaveChangesAsync();

            // Load the message with sender info for response
            await _context.Entry(message)
                .Reference(m => m.Sender)
                .LoadAsync();

            await _context.Entry(message.Sender)
                .Reference(u => u.Profile)
                .LoadAsync();

            var messageViewModel = new DirectMessageViewModel
            {
                Id = message.Id,
                Content = message.Content,
                CreatedAt = message.CreatedAt,
                IsFromCurrentUser = true,
                SenderName = message.Sender.DisplayName,
                SenderAvatarUrl = message.Sender.Profile?.AvatarUrl,
                IsRead = false
            };

            return Json(new { success = true, message = messageViewModel });
        }

        private static FriendStatus GetFriendStatus(Friendship? friendship, string currentUserId, string otherUserId)
        {
            if (friendship == null)
                return FriendStatus.NotFriend;

            return friendship.Status switch
            {
                FriendshipStatus.Accepted => FriendStatus.Friends,
                FriendshipStatus.Blocked => FriendStatus.Blocked,
                FriendshipStatus.Pending => friendship.RequesterId == currentUserId
                    ? FriendStatus.PendingOutgoing
                    : FriendStatus.PendingIncoming,
                _ => FriendStatus.NotFriend
            };
        }
    }

    public class SendMessageRequest
    {
        public string FriendId { get; set; } = string.Empty;
        public string Content { get; set; } = string.Empty;
    }
}
